﻿using CouponApp.Server.Models.DTOs;

namespace CouponApp.Server.Features.CustomDomains.Handlers;


using CouponApp.Server.Data;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;
public class GetCustomDomainsHandler : IRequestHandler<GetCustomDomainsQuery, List<CustomDomainDto>>
{
    private readonly ApplicationDbContext _context;

    public GetCustomDomainsHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<List<CustomDomainDto>> Handle(
        GetCustomDomainsQuery request, 
        CancellationToken cancellationToken)
    {
        return await _context.CustomDomains
            .Where(d => d.OrganizationId == request.OrganizationId)
            .Select(d => new CustomDomainDto
            {
                Id = d.Id,
                DomainName = d.DomainName,
                Status = d.Status,
                ErrorMessage = d.ErrorMessage,
                HasValidSsl = d.HasValidSsl,
              
            })
            .ToListAsync(cancellationToken);
    }
}