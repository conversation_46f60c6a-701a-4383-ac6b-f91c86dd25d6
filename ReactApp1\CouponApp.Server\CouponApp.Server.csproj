﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <InvariantGlobalization>false</InvariantGlobalization>
    
    <SpaRoot>../couponapp-client</SpaRoot>
    
    <SpaProxyLaunchCommand>npm run dev</SpaProxyLaunchCommand>
    <SpaProxyServerUrl>https://localhost:5173</SpaProxyServerUrl>
    <RootNamespace>CouponApp.Server</RootNamespace>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="DistributedLock.Postgres" Version="1.3.0" />
    <PackageReference Include="Logto.AspNetCore.Authentication" Version="0.1.2" />
    <PackageReference Include="Mediator.Abstractions" Version="3.0.0-preview.27" />
    <PackageReference Include="Mediator.SourceGenerator" Version="3.0.0-preview.27">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.SpaProxy">
      <Version>9.0.3</Version>
    </PackageReference>
    <PackageReference Include="Minio" Version="6.0.0" />
    <PackageReference Include="Nanoid" Version="3.1.0" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageReference Include="OneOf" Version="3.0.271" />
    <PackageReference Include="Polly" Version="8.5.2" />
    <PackageReference Include="Resend" Version="0.1.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.5" />
    <PackageReference Include="Slugify.Core" Version="5.1.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.0.0" />
	  <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.3" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.3" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.3">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.3">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\couponapp-client\couponapp-client.esproj">
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
  </ItemGroup>

  <ItemGroup>
    <Content Include="..\.dockerignore">
      <Link>.dockerignore</Link>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Migrations\" />
  </ItemGroup>

</Project>
