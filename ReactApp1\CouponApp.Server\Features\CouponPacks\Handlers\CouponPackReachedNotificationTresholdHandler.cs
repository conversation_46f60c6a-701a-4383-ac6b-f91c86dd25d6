﻿// FILE: '~/CouponApp.Server/Featuers/CouponPacks/Handlers/CouponPackReachedNotificationTresholdHandler.cs'

using CouponApp.Server.Services;
using Mediator;
using Microsoft.Extensions.Caching.Distributed;
using System.Text.Json;

namespace CouponApp.Server.Features.CouponPacks.Handlers;

public class CouponPackReachedNotificationTresholdHandler : INotificationHandler<CouponPackReachedNotificationTreshold>
{
    private readonly IEmailService _emailService;
    private readonly IDistributedCache _cache;
    private readonly ILogger<CouponPackReachedNotificationTresholdHandler> _logger;
    private const int DebounceIntervalMinutes = 30;

    public CouponPackReachedNotificationTresholdHandler(
        IEmailService emailService, 
        IDistributedCache cache,
        ILogger<CouponPackReachedNotificationTresholdHandler> logger)
    {
        _emailService = emailService;
        _cache = cache;
        _logger = logger;
    }

    public async ValueTask Handle(CouponPackReachedNotificationTreshold notification, CancellationToken cancellationToken)
    {
        var cacheKey = $"NotificationSent:{notification.CampaignId}";

        if (notification.RemainingCoupons <= 0)
        {
            await SendNotificationAndUpdateCache(notification, cacheKey, cancellationToken);
            return;
        }

        var lastNotificationSent = await _cache.GetStringAsync(cacheKey, cancellationToken);

        if (string.IsNullOrEmpty(lastNotificationSent) || 
            !DateTime.TryParse(lastNotificationSent, out var lastSentTime) || 
            (DateTime.UtcNow - lastSentTime).TotalMinutes >= DebounceIntervalMinutes)
        {
            await SendNotificationAndUpdateCache(notification, cacheKey, cancellationToken);
        }
        else
        {
            _logger.LogInformation("Notification for campaign {CampaignId} skipped due to debounce interval", notification.CampaignId);
        }
    }

    private async Task SendNotificationAndUpdateCache(
        CouponPackReachedNotificationTreshold notification, 
        string cacheKey, 
        CancellationToken cancellationToken)
    {
        var result = await _emailService.SendCouponsThresholdReachedEmailAsync(
            notification.Email,
            notification.CampaignName,
            notification.CouponPackName,
            notification.RemainingCoupons.ToString());

        if (result.IsT0)
        {
            await _cache.SetStringAsync(
                cacheKey, 
                DateTime.UtcNow.ToString("O"), 
                new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(DebounceIntervalMinutes)
                },
                cancellationToken);

            _logger.LogInformation("Notification sent for campaign {CampaignId}", notification.CampaignId);
        }
        else
        {
            _logger.LogError("Failed to send notification for campaign {CampaignId}: {ErrorMessage}", 
                notification.CampaignId, result.AsT1.Value);
        }
    }
}