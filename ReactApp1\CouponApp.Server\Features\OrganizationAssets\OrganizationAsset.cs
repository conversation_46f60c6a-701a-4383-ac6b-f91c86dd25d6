﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CouponApp.Server.Models.Entities;

public class OrganizationAsset : BaseEntity
{
    [Required]
    public string FileName { get; set; }

    [Required]
    public string FileUrl { get; set; }

    [Required]
    public long FileSize { get; set; } // In bytes
    
    public string ContentType { get; set; }

    public Guid? OrganizationId { get; set; }

    [ForeignKey("OrganizationId")]
    public Organization? Organization { get; set; }

    public DateTime? DeletedAt { get; set; } // For soft deletion if needed
}