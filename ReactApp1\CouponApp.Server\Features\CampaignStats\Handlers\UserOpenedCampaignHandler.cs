﻿using CouponApp.Server.Data;
using CouponApp.Server.Models.Entities;
using Mediator;
using Microsoft.EntityFrameworkCore;

namespace CouponApp.Server.Features.CampaignStats.Handlers
{
    public class UserOpenedCampaignHandler : INotificationHandler<UserOpenedCampaignNotification>
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<UserOpenedCampaignHandler> _logger;

        public UserOpenedCampaignHandler(ApplicationDbContext context, ILogger<UserOpenedCampaignHandler> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async ValueTask Handle(UserOpenedCampaignNotification notification, CancellationToken cancellationToken)
        {
            try
            {
                if (true)
                {
                    Console.Error.WriteLine("UserOpenedCampaignHandler is not yet implemented!");
                    return;
                }
                var campaign = await _context.Campaigns
                    .Include(c => c.CampaignAnalytics)
                    .ThenInclude(a => a.UniqueEnters)
                    .FirstOrDefaultAsync(c => c.Id == notification.CampaignId, cancellationToken);

                if (campaign == null)
                {
                    return;
                }
                
                if (campaign.CampaignAnalytics == null)
                {
                    campaign.CampaignAnalytics = new Models.Entities.CampaignAnalytics();
                    await _context.SaveChangesAsync();
                    
                    _logger.LogError($"Campaign or CampaignAnalytics not found for campaign: {notification.CampaignId}");
                    return;
                }

                campaign.CampaignAnalytics.TotalClicks++;

                var uniqueEnter = campaign.CampaignAnalytics.UniqueEnters.FirstOrDefault(cue => 
                    // cue.IpAddress == notification.Fingerprint.IpAddress ||
                    cue.BrowserFingerprint == notification.Fingerprint.BrowserFingerprint ||
                    cue.LocalStorageValue == notification.Fingerprint.LocalStorageValue);

                if (uniqueEnter == null)
                {
                    uniqueEnter = new CampaignUniqueEnter
                    {
                        IpAddress = notification?.Fingerprint?.IpAddress,
                        BrowserFingerprint = notification?.Fingerprint?.BrowserFingerprint,
                        LocalStorageValue = notification?.Fingerprint?.LocalStorageValue,
                        EnteredAt = DateTime.UtcNow
                    };
                    
                    campaign.CampaignAnalytics.UniqueEnters.Add(uniqueEnter);
                    campaign.CampaignAnalytics.UniqueClicks++;
                }

                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation($"User opened campaign: {notification.CampaignId}, IP: {notification.Fingerprint.IpAddress}, Browser: {notification.Fingerprint.BrowserFingerprint}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error handling UserOpenedCampaignNotification for campaign: {notification.CampaignId}");
            }
        }
    }
}