﻿// File: ~/CouponApp.Server/Controllers/OrganizationsController.cs

using CouponApp.Server.Attributes;
using CouponApp.Server.Extensions;
using CouponApp.Server.Features.Hubspot;
using CouponApp.Server.Features.Organizations;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Services;
using CouponApp.Server.Services.Integrations;
using Mediator;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CouponApp.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class OrganizationsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly IUserProfileService _userProfileService;

    public OrganizationsController(IMediator mediator, IUserProfileService userProfileService)
    {
        _mediator = mediator;
        _userProfileService = userProfileService;
    }

    [HttpGet]
    public async Task<IActionResult> GetUserOrganizations()
    {
        var userId = User.GetId();
        var result = await _userProfileService.GetUserOrganizationsAsync(userId);

        return result.Match<IActionResult>(
            profile => Ok(profile),
            error => NotFound()
        );
    }
    
    [HttpGet("my/{slug}")]
    public async Task<ActionResult<OrganizationDto>> GetMyOrganization(string slug)
    {
        var userId = User.GetId();
        var result = await _mediator.Send(new GetOrganizationForUserByShortIdQuery(userId, slug));

        return result.Match<ActionResult<OrganizationDto>>(
            organization => Ok(organization),
            notFound => NotFound()
        );
    }

    
    [HttpPost]
    public async Task<ActionResult<OrganizationDto>> CreateOrganization([FromBody] CreateOrganizationDto dto)
    {
        var userId = User.GetId();
        var result = await _mediator.Send(new CreateOrganizationCommand(userId, dto));

        return result.Match<ActionResult<OrganizationDto>>(
            organization => Ok(organization),
            alreadyExists => Conflict(alreadyExists.Message),
            userNotFound => NotFound(userNotFound.Message)
        );
    }

    [HttpPut("{id}")]
    [RequirePermission(PermissionType.OrganizationWrite, "id")]
    public async Task<ActionResult<OrganizationDto>> UpdateOrganization(Guid id, [FromBody] UpdateOrganizationDto dto)
    {
        var result = await _mediator.Send(new UpdateOrganizationCommand(id, dto));

        return result.Match<ActionResult<OrganizationDto>>(
            organization => Ok(organization),
            notFound => NotFound()
        );
    }

    [HttpDelete("{id}")]
    [RequirePermission(PermissionType.OrganizationWrite, "id")]
    public async Task<IActionResult> DeleteOrganization(Guid id)
    {
        var result = await _mediator.Send(new DeleteOrganizationCommand(id));

        return result.Match<IActionResult>(
            success => NoContent(),
            notFound => NotFound()
        );
    }
    
    [HttpGet("{id}/members")]
    [RequirePermission(PermissionType.OrganizationRead, "id")]
    public async Task<IActionResult> GetOrganizationMembers(Guid id)
    {
        var result = await _mediator.Send(new GetOrganizationMembersQuery(id));

        return result.Match<IActionResult>(
            members => Ok(members),
            notFound => NotFound()
        );
    }    
    
    [HttpDelete("{organizationId:guid}/members/{userId}")]
    [RequirePermission(PermissionType.OrganizationMemberDelete, "organizationId")]
    public async Task<IActionResult> DeleteMember(Guid organizationId, string userId)
    {
        var result = await _mediator.Send(new DeleteOrganizationMemberCommand(organizationId, userId));
        
        return result.Match<IActionResult>(
            success => NoContent(),
            notFound => NotFound("Organization or member not found"),
            error => BadRequest(error.Message)
        );
    }

    // [HttpGet("{id}/project-campaign-members")]
    // [RequirePermission(PermissionType.OrganizationRead, "id")]
    // public async Task<IActionResult> GetMembersWorkedOnOrganizationCampaigns(Guid id, [FromQuery] string? exceptCampaignId)
    // {
    //     var result = await _mediator.Send(new GetMembersWorkedOnOrganizationCampaignsQuery(id, exceptCampaignId));
    //
    //     return result.Match<IActionResult>(
    //         members => Ok(members),
    //         notFound => NotFound()
    //     );
    // } 

}