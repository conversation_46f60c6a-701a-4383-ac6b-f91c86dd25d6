﻿using System.Net;
using System.Text.Json;
using OneOf.Types;

namespace CouponApp.Server.Services.Integrations;
using OneOf;

public class NangoRequestConfig
{
    public string ConnectionId { get; set; }
    public string ProviderConfigKey { get; set; }
    public string BaseUrlOverride { get; set; }

    public NangoRequestConfig(string connectionId = null, string providerConfigKey = null, string baseUrlOverride = null)
    {
        ConnectionId = connectionId;
        ProviderConfigKey = providerConfigKey;
        BaseUrlOverride = baseUrlOverride;
    }
}

public interface INangoClient
{
    Task<OneOf<T, Error<string>>> GetAsync<T>(string endpoint, NangoRequestConfig? config = null);
    Task<OneOf<T, Error<string>>> PostAsync<T>(string endpoint, object content, NangoRequestConfig? config = null);
    Task<OneOf<T, Error<string>>> DeleteAsync<T>(string endpoint, NangoRequestConfig? config = null);
}

public class NangoClient : INangoClient
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<NangoClient> _logger;

    public NangoClient(HttpClient httpClient, ILogger<NangoClient> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _httpClient.BaseAddress = new Uri("https://api.nango.dev");
    }

    public Task<OneOf<T, Error<string>>> GetAsync<T>(string endpoint, NangoRequestConfig? config = null)
        => SendRequestAsync<T>(HttpMethod.Get, endpoint, null, config);

    public Task<OneOf<T, Error<string>>> PostAsync<T>(string endpoint, object content, NangoRequestConfig? config = null)
        => SendRequestAsync<T>(HttpMethod.Post, endpoint, content, config);

    public Task<OneOf<T, Error<string>>> DeleteAsync<T>(string endpoint, NangoRequestConfig? config = null)
        => SendRequestAsync<T>(HttpMethod.Delete, endpoint, null, config);

    private async Task<OneOf<T, Error<string>>> SendRequestAsync<T>(HttpMethod method, string endpoint, object? content = null, NangoRequestConfig? config = null)
    {
        try
        {
            var request = CreateRequest(method, endpoint, config);
            if (content != null && (method == HttpMethod.Post || method == HttpMethod.Put))
            {
                request.Content = JsonContent.Create(content);
            }

            var response = await SendAndDeserializeAsync<T>(request);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending request to Nango API");
            return new Error<string>($"Error sending request to Nango API: {ex.Message}");
        }
    }

    private HttpRequestMessage CreateRequest(HttpMethod method, string endpoint, NangoRequestConfig? config)
    {
        var request = new HttpRequestMessage(method, $"{endpoint}");
        request.Headers.Add("Authorization", "Bearer c412a3f4-1641-4efa-a387-78270fe431b5");

        if (config != null)
        {
            if (!string.IsNullOrEmpty(config.ConnectionId))
                request.Headers.Add("Connection-Id", config.ConnectionId);

            if (!string.IsNullOrEmpty(config.ProviderConfigKey))
                request.Headers.Add("Provider-Config-Key", config.ProviderConfigKey);

            if (!string.IsNullOrEmpty(config.BaseUrlOverride))
                request.Headers.Add("Base-Url-Override", config.BaseUrlOverride);
        }

        return request;
    }

    private async Task<OneOf<T, Error<string>>> SendAndDeserializeAsync<T>(HttpRequestMessage request)
    {
        try
        {
            var response = await _httpClient.SendAsync(request);
            var content = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                return new Error<string>(GetErrorMessage(response, content));
            }

            if (response.StatusCode == HttpStatusCode.NoContent)
            {
                return default(T);
            }

            if (typeof(T) == typeof(string))
            {
                return (T)(object)content;
            }

            if (string.IsNullOrWhiteSpace(content))
            {
                return default(T);
            }

            var result = JsonSerializer.Deserialize<T>(content, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            return result != null ? result : new Error<string>("Failed to deserialize response");
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP request failed");
            return new Error<string>($"HTTP request failed: {ex.Message}");
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Deserialization failed");
            return new Error<string>($"Deserialization failed: {ex.Message}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unexpected error occurred");
            return new Error<string>($"An unexpected error occurred: {ex.Message}");
        }
    }
    
    private string GetErrorMessage(HttpResponseMessage response, string content)
    {
        try
        {
            var errorObject = JsonSerializer.Deserialize<JsonElement>(content);
            return "" + errorObject;
        }
        catch (JsonException)
        {
            return $"Error {(int)response.StatusCode}: {response.ReasonPhrase}";
        }
    }
}