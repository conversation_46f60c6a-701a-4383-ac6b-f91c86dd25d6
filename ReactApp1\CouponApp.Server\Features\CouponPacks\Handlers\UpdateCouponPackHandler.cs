﻿using CouponApp.Server.Data;
using CouponApp.Server.Mappers;
using CouponApp.Server.Models.DTOs;
using Mediator;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.CouponPacks.Handlers;

public class UpdateCouponPackHandler : IRequestHandler<UpdateCouponPackCommand, OneOf<CouponPackDto, NotFound>>
{
    private readonly ApplicationDbContext _context;

    public UpdateCouponPackHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<CouponPackDto, NotFound>> Handle(UpdateCouponPackCommand request, CancellationToken cancellationToken)
    {
        var couponPack = await _context.CouponPacks.FindAsync(new object[] { request.Id }, cancellationToken);

        if (couponPack == null)
        {
            return new NotFound();
        }

        couponPack.Name = request.UpdateDto.Name;
        couponPack.DeleteCouponOnRedeem = request.UpdateDto.DeleteCouponOnRedeem;
        couponPack.ShouldCheckingForUserParticipation = request.UpdateDto.ShouldCheckingForUserParticipation;
        couponPack.NotifyThreshold = request.UpdateDto.NotifyThreshold;
        couponPack.InterruptThreshold = request.UpdateDto.InterruptThreshold;
        couponPack.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync(cancellationToken);

        return couponPack.ToDto();
    }
}