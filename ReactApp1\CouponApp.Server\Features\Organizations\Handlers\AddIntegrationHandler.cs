﻿using CouponApp.Server.Data;
using CouponApp.Server.Mappers;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Services.Integrations;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Organizations.Handlers;

public class AddIntegrationHandler : IRequestHandler<AddIntegrationCommand, OneOf<OrganizationIntegrationDto, NotFound, Error<string>>>
{
    private readonly ApplicationDbContext _context;

    public AddIntegrationHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<OrganizationIntegrationDto, NotFound, Error<string>>> Handle(AddIntegrationCommand request, CancellationToken cancellationToken)
    {
        var organization = await _context.Organizations.FindAsync(new object[] { request.Dto.OrganizationId }, cancellationToken);
        if (organization == null)
        {
            return new NotFound();
        }
        
        if (!Enum.IsDefined(typeof(IntegrationType), request.Dto.IntegrationType))
        {
            return new Error<string>("Invalid integration type");
        }

        var existingIntegration = await _context.OrganizationIntegrations
            .FirstOrDefaultAsync(oi => oi.OrganizationId == request.Dto.OrganizationId && oi.IntegrationType == request.Dto.IntegrationType, cancellationToken);

        if (existingIntegration != null)
        {
            return new Error<string>($"'{request.Dto.IntegrationType}' integration already exists for this organization");
        }

     

        var integration = new OrganizationIntegration
        {
            OrganizationId = request.Dto.OrganizationId,
            IntegrationType = request.Dto.IntegrationType,
            ConnectionId = request.Dto.ConnectionId,
            AdditionalData = request.Dto.AdditionalData
        };

        _context.OrganizationIntegrations.Add(integration);
        await _context.SaveChangesAsync(cancellationToken);

        return integration.ToDto();
    }
}