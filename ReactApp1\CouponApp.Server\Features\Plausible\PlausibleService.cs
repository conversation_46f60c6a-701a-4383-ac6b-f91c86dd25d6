﻿using System.Text.Json;
using CouponApp.Server.Models.DTOs.Plausible;
using Microsoft.Extensions.Options;
using OneOf;
using OneOf.Types;
using Polly;
using Polly.Retry;

namespace CouponApp.Server.Services;



public interface IPlausibleService
{
    Task<OneOf<PlausibleResponseDto, Error<string>>> GetEventAnalyticsAsync(Guid campaignId, string dateRange, CancellationToken cancellationToken = default);
    Task<OneOf<PlausibleResponseDto, Error<string>>> GetVisitorAnalyticsAsync(Guid campaignId, string dateRange, CancellationToken cancellationToken = default);
}

public class PlausibleService : IPlausibleService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<PlausibleService> _logger;
    private readonly AsyncRetryPolicy<HttpResponseMessage> _retryPolicy;
    private readonly IConfiguration _configuration;
    private readonly string _siteId;
    
    public PlausibleService(
        IHttpClientFactory httpClientFactory,
        ILogger<PlausibleService> logger, IConfiguration configuration)
    {
        _httpClient = httpClientFactory.CreateClient("PlausibleApi");
        _logger = logger;
        _configuration = configuration;
        _siteId = _configuration.GetValue<string>("Plausible:SiteId");

        if (_siteId == null)
        {
            throw new Exception("Plausible SiteId not configured properly.");
        }
        
        _retryPolicy = Policy<HttpResponseMessage>
            .Handle<HttpRequestException>()
            .OrResult(msg => !msg.IsSuccessStatusCode)
            .WaitAndRetryAsync(3, retryAttempt => 
                TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));
    }


    public async Task<OneOf<PlausibleResponseDto, Error<string>>> GetVisitorAnalyticsAsync(Guid campaignId, string dateRange, CancellationToken cancellationToken = default)
    {
        var requestDto = new PlausibleRequestDto
        {
            SiteId = _siteId,
            Metrics = ["pageviews", "visitors", "visit_duration"],
            DateRange = dateRange,
            Dimensions = [],
            Filters = [new() {"contains", "event:props:campaign", new List<string> {$"{campaignId}"}}]
        };

        return await SendPlausibleRequest(requestDto, cancellationToken);
    }

    public async Task<OneOf<PlausibleResponseDto, Error<string>>> GetEventAnalyticsAsync(Guid campaignId, string dateRange, CancellationToken cancellationToken = default)
    {
        var requestDto = new PlausibleRequestDto
        {
            SiteId = _siteId,
            Metrics = [ "events", "visitors" ],
            DateRange = dateRange,
            Dimensions = ["event:name", "event:props:widgetId", "event:props:sceneId"],
            Filters = [new() {"contains", "event:props:campaign", new List<string> {$"{campaignId}"}}]
        };

        var response = await SendPlausibleRequest(requestDto, cancellationToken);
        return response;
    }
    
    
    public async Task<OneOf<PlausibleResponseDto, Error<string>>> SendPlausibleRequest(PlausibleRequestDto requestDto, CancellationToken cancellationToken = default)
    {
        try
        {
            var request = new HttpRequestMessage(HttpMethod.Post, "api/v2/query")
            {
                Content = JsonContent.Create(requestDto)
            };

            var response = await _httpClient.SendAsync(request, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                return new Error<string>($"Plausible API request failed with status code: {response.StatusCode}");
            }

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var plausibleResponse = JsonSerializer.Deserialize<PlausibleResponseDto>(responseContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (plausibleResponse == null)
            {
                return new Error<string>("Failed to deserialize Plausible response.");
            }

            return plausibleResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while sending request to Plausible API");
            return new Error<string>($"An error occurred while sending request to Plausible API: {ex.Message}");
        }
    }
}