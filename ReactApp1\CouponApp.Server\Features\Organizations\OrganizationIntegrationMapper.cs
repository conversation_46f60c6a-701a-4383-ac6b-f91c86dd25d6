﻿using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;

namespace CouponApp.Server.Mappers;

public static class OrganizationIntegrationMapper
{
    public static OrganizationIntegrationDto ToDto(this OrganizationIntegration integration)
    {
        return new OrganizationIntegrationDto
        {
            Id = integration.Id,
            IntegrationType = integration.IntegrationType,
            ConnectionId = integration.ConnectionId,
           AdditionalData = integration.AdditionalData,
            CreatedAt = integration.CreatedAt,
            UpdatedAt = integration.UpdatedAt
        };
    }
}