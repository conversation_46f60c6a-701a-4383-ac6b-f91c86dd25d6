﻿using System.Net;
using CouponApp.Server.Helpers;
using CouponApp.Server.Models;
using Microsoft.AspNetCore.Components.Forms;

namespace CouponApp.Server.Extensions;

public static class HttpContextExtensions
{
    public static string? GetIpAddress(this HttpContext context)
    {
        string? ip = null;

        // Check for Cloudflare IP
        ip = context.Request.Headers["CF-Connecting-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(ip))
            return ip;

        // Check for X-Forwarded-For header
        ip = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(ip))
        {
            // X-Forwarded-For may contain multiple IPs, take the first one
            return ip.Split(',')[0].Trim();
        }

        // If no proxy headers found, fallback to the remote IP address
        ip = context.Connection.RemoteIpAddress?.ToString();

        // Handle IPv6 to IPv4 mapping if necessary
        if (ip != null && IPAddress.TryParse(ip, out var parsedIp))
        {
            if (parsedIp.IsIPv4MappedToIPv6)
            {
                ip = parsedIp.MapToIPv4().ToString();
            }
        }

        return ip;
    }
    
    public static SessionFingerprint? GetFingerprint(this HttpContext context)
    {
        //Style-Settings is a custom header that contains the encrypted fingerprint. 
        //The name is just to make it harder at least to find at first look. 
        var userFingerprint = context.Request.Headers["Style-Settings"].FirstOrDefault() ?? "";
        var decrypted = BrowserFingerprintHelpers.DecryptFingerprint(userFingerprint);

        if (decrypted is not null)
        {
            return new SessionFingerprint
            {
                IpAddress = context.GetIpAddress(),
                BrowserFingerprint = decrypted.Value.GetProperty("f").GetString(), 
                LocalStorageValue = decrypted.Value.GetProperty("s").GetString()
            };
        }
        
        return new SessionFingerprint
        {
            IpAddress = context.GetIpAddress(),
            BrowserFingerprint = null, 
            LocalStorageValue = null
        };;
    }
}