﻿using CouponApp.Server.Features.Shopify;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Services.NodeExecution.Interfaces;
using Mediator;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Services.Executors;

public class ShopifyAddCustomerNodeExecutor : INodeExecutor
{
    private readonly ILogger<ShopifyAddCustomerNodeExecutor> _logger;
    private readonly IVariablesStringInterpolationService _variablesStringInterpolationService;
    private readonly IMediator _mediator;

    public ShopifyAddCustomerNodeExecutor(
        ILogger<ShopifyAddCustomerNodeExecutor> logger,
        IVariablesStringInterpolationService variablesStringInterpolationService,
        IMediator mediator)
    {
        _logger = logger;
        _variablesStringInterpolationService = variablesStringInterpolationService;
        _mediator = mediator;
    }

    public async Task<OneOf<Success, Error<string>>> ExecuteAsync(Guid campaignId, CampaignFlowNode node, Dictionary<string, object> variables)
    {
        try
        {
            node.Payload.TryGetValue("email", out var rawEmail);
            node.Payload.TryGetValue("phone", out var rawPhone);
            node.Payload.TryGetValue("firstName", out var rawFirstName);
            node.Payload.TryGetValue("lastName", out var rawLastName);
            node.Payload.TryGetValue("tag", out var rawTag);

            var email = _variablesStringInterpolationService.InterpolateVariables(rawEmail?.ToString() ?? string.Empty, variables);
            var phone = _variablesStringInterpolationService.InterpolateVariables(rawPhone?.ToString() ?? string.Empty, variables);
            var firstName = _variablesStringInterpolationService.InterpolateVariables(rawFirstName?.ToString() ?? string.Empty, variables);
            var lastName = _variablesStringInterpolationService.InterpolateVariables(rawLastName?.ToString() ?? string.Empty, variables);
            var tag = _variablesStringInterpolationService.InterpolateVariables(rawTag?.ToString() ?? string.Empty, variables);

            var result = await _mediator.Send(new AddShopifyCustomerCommand(campaignId, email, phone, firstName, lastName, tag));

            return result.Match<OneOf<Success, Error<string>>>(
                success => new Success(),
                error => new Error<string>($"Failed to add customer to Shopify: {error.Value}")
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing ShopifyAddCustomerNode node {NodeId}", node.Id);
            return new Error<string>($"Error executing ShopifyAddCustomerNode: {ex.Message}");
        }
    }
}