﻿using CouponApp.Server.Data;
using CouponApp.Server.Mappers;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Campaigns.Handlers;

public class GetCampaignsByOrganizationIdQueryHandler : IRequestHandler<GetCampaignsByOrganizationIdQuery, OneOf<IEnumerable<CampaignMiniDto>, Error<string>>>
{
    private readonly ApplicationDbContext _context;

    public GetCampaignsByOrganizationIdQueryHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<IEnumerable<CampaignMiniDto>, Error<string>>> Handle(
        GetCampaignsByOrganizationIdQuery request, 
        CancellationToken cancellationToken)
    {
        try
        {
            var campaigns = await _context.Campaigns
                .Where(c => c.WorkspaceId == request.WorkspaceId)
                .OrderByDescending(c => c.CreatedAt)
                .Select(c => c.ToMini<PERSON>to())
                .ToListAsync(cancellationToken);

            return campaigns;
        }
        catch (Exception ex)
        {
            return new Error<string>($"Error retrieving campaigns: {ex.Message}");
        }
    }
}