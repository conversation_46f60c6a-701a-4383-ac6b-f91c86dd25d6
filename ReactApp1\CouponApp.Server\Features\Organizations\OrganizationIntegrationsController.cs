﻿using CouponApp.Server.Attributes;
using CouponApp.Server.Extensions;
using CouponApp.Server.Features.Hubspot;
using CouponApp.Server.Features.Organizations;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Services;
using CouponApp.Server.Services.Integrations;
using Mediator;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CouponApp.Server.Controllers;

[ApiController]
[Route("api/organizations/{organizationId}/integrations")]
[Authorize]
[RequirePermission(PermissionType.OrganizationRead, "organizationId")]
public class OrganizationIntegrationsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly INangoConnectionIdService _nangoConnectionIdService;
    
    public OrganizationIntegrationsController(IMediator mediator, INangoConnectionIdService nangoConnectionIdService)
    {
        _mediator = mediator;
        _nangoConnectionIdService = nangoConnectionIdService;
    }

    [HttpGet("{integrationType}/create-data")]
    public async Task<IActionResult> GetCreationData(Guid organizationId, IntegrationType integrationType)
    {
        var hasUser = await _mediator.Send(new CheckOrganizationMembershipQuery(organizationId, User.GetId()));
        if (!hasUser)
        {
            return Unauthorized("You are not a member of this organization");
        }
        
        var data = await _nangoConnectionIdService.GenerateConnectionCreationData(organizationId, integrationType);
        
        return data.Match<IActionResult>(
            data => Ok(data),
            error => BadRequest(error.Value)
        );
        
    }
       
    [HttpPost]
    public async Task<IActionResult> AddIntegration(Guid organizationId, [FromBody] AddOrganizationIntegrationDto dto)
    {
        if (organizationId != dto.OrganizationId)
        {
            return BadRequest("Organization ID mismatch");
        }

        var hasUser = await _mediator.Send(new CheckOrganizationMembershipQuery(organizationId, User.GetId()));
        if (!hasUser)
        {
            return Unauthorized("You are not a member of this organization");
        }

        var result = await _mediator.Send(new AddIntegrationCommand(dto));

        return result.Match<IActionResult>(
            integration => Ok(integration),
            notFound => NotFound(),
            error => BadRequest(error.Value)
        );
    }

    [HttpGet]
    public async Task<IActionResult> GetIntegrations(Guid organizationId)
    {
        var hasUser = await _mediator.Send(new CheckOrganizationMembershipQuery(organizationId, User.GetId()));
        if (!hasUser)
        {
            return Unauthorized("You are not a member of this organization");
        }

        var result = await _mediator.Send(new GetIntegrationsQuery(organizationId));

        return result.Match<IActionResult>(
            integrations => Ok(integrations),
            notFound => NotFound()
        );
    }

    [HttpGet("mail-chimp/lists")]
    public async Task<IActionResult> GetMailchimpLists(Guid organizationId)
    {
        var hasUser = await _mediator.Send(new CheckOrganizationMembershipQuery(organizationId, User.GetId()));
        if (!hasUser)
        {
            return Unauthorized("You are not a member of this organization");
        }

        var result = await _mediator.Send(new GetMailchimpListsQuery(organizationId));

        return result.Match<IActionResult>(
            lists => Ok(lists),
            notFound => NotFound(),
            error => BadRequest(error.Value)
        );
    }


    [HttpGet("hubspot/properties")]
    public async Task<IActionResult> GetProperties(Guid organizationId)
    {
        var hasUser = await _mediator.Send(new CheckOrganizationMembershipQuery(organizationId, User.GetId()));
        if (!hasUser)
        {
            return Unauthorized("You are not a member of this organization");
        }

        var result = await _mediator.Send(new GetHubspotPropertiesQuery(organizationId));

        return result.Match<IActionResult>(
            lists => Ok(lists),
            error => BadRequest(error.Value)
        );
    }

    [HttpDelete("{integrationType}")]
    public async Task<IActionResult> DisconnectIntegration(Guid organizationId, IntegrationType integrationType)
    {
        var hasUser = await _mediator.Send(new CheckOrganizationMembershipQuery(organizationId, User.GetId()));
        if (!hasUser)
        {
            return Unauthorized("You are not a member of this organization");
        }

        var result = await _mediator.Send(new DisconnectIntegrationCommand(organizationId, integrationType));

        return result.Match<IActionResult>(
            success => Ok(new {message = "Integration disconnected successfully"}),
            error => BadRequest(error.Value)
        );
    }
}