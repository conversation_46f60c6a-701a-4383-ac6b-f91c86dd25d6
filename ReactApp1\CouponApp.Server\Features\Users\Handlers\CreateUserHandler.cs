﻿using CouponApp.Server.Data;
using CouponApp.Server.Features.Organizations;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;
using Slugify;

namespace CouponApp.Server.Features.Users.Handlers;

public class CreateUserHandler : IRequestHandler<CreateUserCommand, OneOf<bool, Error<string>>>
{
    private readonly ApplicationDbContext _context;
    private readonly SlugHelper _slugHelper;
    private readonly IMediator _mediator;
    
    public CreateUserHandler(ApplicationDbContext context, IMediator mediator)
    {
        _context = context;
        _mediator = mediator;
        _slugHelper = new SlugHelper();
    }

    public async ValueTask<OneOf<bool, Error<string>>> Handle(CreateUserCommand request,
        CancellationToken cancellationToken)
    {
        var newUser = request.UserEntity;

        var user = await _context.UserProfiles
            // .Include(u => u.Organizations)
            .FirstOrDefaultAsync(u => u.Id == newUser.Id);

        var userOrganizations = await _context.Organizations
            .Include(o => o.Members)
            .Where(o => o.Members.Any(m => m.UserProfileId == newUser.Id))
            .ToListAsync();
        
        if (user == null)
        {
            user = new UserProfile
            {
                Id = newUser.Id,
                Email = newUser.PrimaryEmail,
                FullName = newUser.Name ?? ""
            };

            _context.UserProfiles.Add(user);
            await _context.SaveChangesAsync();
        }

        if (!userOrganizations.Any())
        {
            var name = (user.FullName?.Split(" ")[0] ?? user.Email ?? user.Id) + "'s Workspace";
            var createOrganizationCommand = new CreateOrganizationCommand(
                newUser.Id,
                new CreateOrganizationDto
                {
                    Name = name
                }
            );
            await _mediator.Send(createOrganizationCommand);
        }
        
        return true;
    }
}