﻿namespace CouponApp.Server.Services.Integrations;

public enum IntegrationType
{
    Mailchimp = 0,
    <PERSON><PERSON><PERSON> = 1,
    GoogleAnalytics = 2,
    MetaPixel = 3,
    TiktokAdsPixel = 4,
    Shopify = 5
}

public static class OrganizationIntegrationExtensions
{
    public static string ToNangoProviderConfigKey(this IntegrationType integrationType)
    {
        return integrationType switch
        {
            IntegrationType.Mailchimp => "mailchimp",
            IntegrationType.Hubspot => "hubspot",
            IntegrationType.Shopify => "shopify",
            _ => throw new ArgumentException($"Unsupported integration type: {integrationType}")
        };
    }
    
    public static bool IsNangoIntegration(this IntegrationType integrationType)
    {
        return integrationType switch
        {
            IntegrationType.Mailchimp => true,
            IntegrationType.Hubspot => true,
            IntegrationType.Shopify => true,
            _ => false
        };
    }
}