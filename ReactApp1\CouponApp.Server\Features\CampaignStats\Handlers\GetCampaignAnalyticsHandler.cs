﻿using CouponApp.Server.Data;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Services;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf.Types;
using OneOf;

namespace CouponApp.Server.Features.CampaignStats.Handlers
{
    public class GetCampaignAnalyticsHandler : IRequestHandler<GetCampaignAnalyticsQuery, OneOf<CampaignAnalyticsDto, NotFound>>
    {
        private readonly ApplicationDbContext _context;
        private readonly IPlausibleService _plausibleService;

        public GetCampaignAnalyticsHandler(ApplicationDbContext context, IPlausibleService plausibleService)
        {
            _context = context;
            _plausibleService = plausibleService;
        }

        public async ValueTask<OneOf<CampaignAnalyticsDto, NotFound>> Handle(GetCampaignAnalyticsQuery request, CancellationToken cancellationToken)
        {
            var campaign = await _context.Campaigns
                .FirstOrDefaultAsync(c => c.Id == request.CampaignId, cancellationToken);

            if (campaign == null)
            {
                return new NotFound();
            }


            var eventAnalytics = await _plausibleService.GetEventAnalyticsAsync(campaign.Id, request.DateRange);

            if(!eventAnalytics.IsT0)
            {
                return new NotFound(); //TODO Return proper error
            }
            
            
            var visitorAnalytics = await _plausibleService.GetVisitorAnalyticsAsync(campaign.Id, request.DateRange);   
            if(!visitorAnalytics.IsT0)
            {
                return new NotFound(); //TODO Return proper error
            }
            

            return new CampaignAnalyticsDto
            {
                CampaignId = campaign.Id,
                CampaignName = campaign.Name,
                // TotalClicks = analytics.TotalClicks,
                // UniqueClicks = analytics.UniqueClicks,
                VisitorAnalytics = visitorAnalytics.AsT0.Results,
                EventAnalytics = eventAnalytics.AsT0.Results
            };
        }
    }
}