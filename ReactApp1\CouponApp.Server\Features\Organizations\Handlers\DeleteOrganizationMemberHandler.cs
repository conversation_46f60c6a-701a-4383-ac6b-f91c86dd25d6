﻿using CouponApp.Server.Data;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Models.Errors;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Organizations.Handlers;

public class DeleteOrganizationMemberHandler : IRequestHandler<DeleteOrganizationMemberCommand, OneOf<Success, NotFound, UnauthorizedError>>
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<DeleteOrganizationMemberHandler> _logger;

    public DeleteOrganizationMemberHandler(ApplicationDbContext context, ILogger<DeleteOrganizationMemberHandler> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async ValueTask<OneOf<Success, NotFound, UnauthorizedError>> Handle(DeleteOrganizationMemberCommand request, CancellationToken cancellationToken)
    {
        using var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);

        try
        {
            var organization = await _context.Organizations
                .Include(o => o.Members)
                .FirstOrDefaultAsync(o => o.Id == request.OrganizationId, cancellationToken);

            if (organization == null)
            {
                return new NotFound();
            }

            var member = organization.Members.FirstOrDefault(m => m.UserProfileId == request.UserId);
            if (member == null)
            {
                return new NotFound();
            }

            if (member.Role == UserOrganizationRole.Owner)
            {
                return new UnauthorizedError("Cannot remove the owner of the organization");
            }

            // Remove from organization
            organization.Members.Remove(member);

            await _context.SaveChangesAsync(cancellationToken);
            await transaction.CommitAsync(cancellationToken);

            _logger.LogInformation("User {UserId} removed from organization {OrganizationId} and its projects", request.UserId, request.OrganizationId);

            return new Success();
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync(cancellationToken);
            _logger.LogError(ex, "Error removing user {UserId} from organization {OrganizationId}", request.UserId, request.OrganizationId);
            return new UnauthorizedError("An error occurred while removing the user from the organization");
        }
    }
}