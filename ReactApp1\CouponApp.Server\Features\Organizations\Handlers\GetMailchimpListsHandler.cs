﻿// File: ~/CouponApp.Server/Features/Organizations/Handlers/GetMailchimpListsHandler.cs

using CouponApp.Server.Data;
using CouponApp.Server.Services.Integrations;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Organizations.Handlers;

public class GetMailchimpListsHandler : IRequestHandler<GetMailchimpListsQuery, OneOf<object, NotFound, Error<string>>>
{
    private readonly ApplicationDbContext _context;
    private readonly IMediator _mediator;

    public GetMailchimpListsHandler(ApplicationDbContext context, IMediator mediator)
    {
        _context = context;
        _mediator = mediator;
    }

    public async ValueTask<OneOf<object, NotFound, Error<string>>> Handle(GetMailchimpListsQuery request, CancellationToken cancellationToken)
    {
        var integration = await _context.OrganizationIntegrations
            .FirstOrDefaultAsync(oi => oi.OrganizationId == request.OrganizationId && oi.IntegrationType == IntegrationType.Mailchimp, cancellationToken);
        
        if (integration == null)
        {
            return new NotFound();
        }

        var mailchimpListsQuery = new Features.Mailchimp.GetMailchimpListsQuery(integration.ConnectionId);
        var result = await _mediator.Send(mailchimpListsQuery, cancellationToken);

        return result.Match<OneOf<object, NotFound, Error<string>>>(
            success => success.Lists.Select(item => new { item.Id, item.Name }).ToList(),
            error => new Error<string>(error.Value)
        );
    }
}