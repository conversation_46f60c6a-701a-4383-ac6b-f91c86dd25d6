﻿using CouponApp.Server.Data;
using CouponApp.Server.Features.Organizations;
using CouponApp.Server.Mappers;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Services;

public interface IUserProfileService
{
    Task<OneOf<UserProfileDto, NotFound>> GetUserProfileAsync(string userId);
    Task<OneOf<ICollection<OrganizationMiniDto>, NotFound>> GetUserOrganizationsAsync(string userId);
}

public class UserProfileService : IUserProfileService
{
    private readonly ApplicationDbContext _context;

    public UserProfileService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<OneOf<ICollection<OrganizationMiniDto>, NotFound>> GetUserOrganizationsAsync(string userId)
    {
        var userOrganizations = await _context.Organizations
            .Include(o => o.Members)
            .Where(o => o.Members.Any(m => m.UserProfileId == userId))
            .Select(o => o.ToMiniDto())
            .ToListAsync();

        return userOrganizations;
    }
    

    public async Task<OneOf<UserProfileDto, NotFound>> GetUserProfileAsync(string userId)
    {
        var user = await _context.UserProfiles
            .FirstOrDefaultAsync(u => u.Id == userId);

        if (user == null)
        {
            return new NotFound();
        }
        
        var profile = new UserProfileDto
        {
            Email = user.Email,
            Name = user.FullName
        };

        return profile;
    }
}