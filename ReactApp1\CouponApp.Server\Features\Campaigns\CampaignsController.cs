﻿using System.Security.Claims;
using System.Text.RegularExpressions;
using CouponApp.Server.Attributes;
using CouponApp.Server.Extensions;
using CouponApp.Server.Features.Campaigns;
using CouponApp.Server.Features.CampaignStats;
using CouponApp.Server.Features.Newsletter;
using CouponApp.Server.Mappers;
using CouponApp.Server.Models;
using Microsoft.AspNetCore.Mvc;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Models.Enums;
using CouponApp.Server.Services;
using Mediator;
using Microsoft.AspNetCore.Authorization;

namespace CouponApp.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
[Authorize]
public class CampaignsController : ControllerBase
{
    private readonly IMediator _mediator;

    public CampaignsController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpGet("{idOrSlug}")]
    [AllowAnonymous]
    public async Task<ActionResult<PublicCampaignDataDto>> GetCampaignForUser(string idOrSlug)
    {
         var fingerprint = HttpContext.GetFingerprint();
         if (fingerprint is null)
         {
             //This is a fake message. This error means that the fingerprinting failed on user's browser.
             return BadRequest("This site works only with javascript enabled.");
         }
        // SessionFingerprint fingerprint = null;

        var hostname = Request.Host.Value;
        
        var result = await _mediator.Send(new GetPublicCampaignQuery(idOrSlug, fingerprint, hostname));

        if(result.IsT0) {
           // TODO _mediator.Publish(new UserOpenedCampaignNotification(id, fingerprint));
        }
        
        return result.Match<ActionResult<PublicCampaignDataDto>>(
            campaign =>
            {
                if (campaign.Campaign.HasNoCouponsLeft == true)
                {
                    return NotFound("We are out of coupons for now. Please try again later.");
                }

                return Ok(campaign);
            },
            notFound => NotFound()
        );
    }  
    
    [HttpPut("{campaignId:guid}/newsletter/{listId}/email/{email}")]
    [AllowAnonymous]
    public async Task<IActionResult> AddUserToNewsletter(Guid campaignId, string listId, string email)
    {
        //Validate email address
        var isValidEmail = Regex.IsMatch(email, @"^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$");
        if (!isValidEmail)
        {
            return BadRequest("Please enter a valid email address.");
        }
        
        var result = await _mediator.Send(new AddUserToNewsletterCommand(campaignId, listId, email));

        return result.Match<IActionResult>(
            success => Ok(new { message = "User added to newsletter successfully" }),
            notFound => NotFound(),
            error => BadRequest(error.Value)
        );
    }    
    
    [HttpGet("{campaignId:guid}/newsletter/{listId}/email/{email}")]
    [AllowAnonymous]
    public async Task<IActionResult> CheckSubscriptionStatus(Guid campaignId, string listId, string email)
    {
        var result = await _mediator.Send(new CheckSubscriptionStatusQuery(campaignId, listId, email));

        return result.Match<IActionResult>(
            isSubscribed => Ok(new { isSubscribed }),
            notFound => NotFound(),
            error => BadRequest(error.Value)
        );
    }

    [HttpGet("edit/{id:guid}")]
    [RequirePermission(PermissionType.CampaignWrite, "id")]
    public async Task<ActionResult<CampaignEditorDto>> GetCampaignForEdit(Guid id)
    {
        var result = await _mediator.Send(new GetCampaignForEditorQuery(id));
        
        return result.Match<ActionResult<CampaignEditorDto>>(
            campaign => Ok(campaign),
            notFound => NotFound()
        );
    }

    
    [HttpDelete("{id:guid}")]
    [RequirePermission(PermissionType.CampaignWrite, "id")]
    public async Task<IActionResult> DeleteCampaign(Guid id, bool deleteAssets = false)
    {
        var result = await _mediator.Send(new DeleteCampaignCommand(id));
        
        return result.Match<IActionResult>(
            success => NoContent(),
            notFound => NotFound()
        );
    }
    
    [HttpPost("{id}/execute-node")]
    [AllowAnonymous]
    public async Task<IActionResult> ExecuteNode(Guid id, [FromBody] ExecuteNodeRequest request)
    {
        var result = await _mediator.Send(new ExecuteNodeCommand(id, request.NodeId, request.Variables));

        return result.Match<IActionResult>(
            success => Ok(),
            notFound => NotFound(),
            error => BadRequest(error)
        );
    }

    [HttpPut("{id:guid}")]
    [RequirePermission(PermissionType.CampaignWrite, "id")]
    public async Task<ActionResult<CampaignEditorDto>> UpdateCampaign(Guid id, [FromBody] CampaignUpdateDto campaignUpdateDto)
    {
        var result = await _mediator.Send(new UpdateCampaignCommand(id, campaignUpdateDto));

        return result.Match<ActionResult<CampaignEditorDto>>(
            updatedCampaign => Ok(updatedCampaign),
            notFound => NotFound(),
            error => BadRequest(error.Value)
        );
    }

    [HttpGet("/campaigns2/{idOrSlug}")]
    [AllowAnonymous]
    public async Task<ActionResult> GetCampaignForUserStatic(string idOrSlug)
    {
        var fingerprint = HttpContext.GetFingerprint();
        if (fingerprint is null)
        {
            return BadRequest("This site works only with javascript enabled.");
        }
        
        var hostname = Request.Host.Value;
        var result = await _mediator.Send(new GetPublicCampaignQuery(idOrSlug, fingerprint, hostname));
        
        if(result.IsT0) {
            var campaign = result.AsT0;
            
            // Read the index.html template
            var path = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "index.html");
            if (!System.IO.File.Exists(path))
            {
                path = Path.Combine("C:\\Users\\<USER>\\RiderProjects\\ReactIdentity\\ReactApp1\\couponapp-client\\apps\\campaign-player\\dist\\index.html");
            }
            string indexHtml = System.IO.File.ReadAllText(path);
            
            // Inject initial state data
            string initialState = System.Text.Json.JsonSerializer.Serialize(new {
                campaign = campaign,
                preloaded = true
            });
            
            // Replace placeholder or add to head
            indexHtml = indexHtml.Replace("<!-- INITIAL_STATE -->", 
                $"<script>window.__INITIAL_STATE__ = {initialState};</script>");
            
         
            
            return Content(indexHtml, "text/html");
        }
        
        return NotFound();
    }
}