﻿// File: ~/CouponApp.Server/Features/Organizations/Handlers/CheckOrganizationMembershipHandler.cs

using CouponApp.Server.Data;
using Mediator;
using Microsoft.EntityFrameworkCore;

namespace CouponApp.Server.Features.Organizations.Handlers;

public class CheckOrganizationMembershipHandler : IRequestHandler<CheckOrganizationMembershipQuery, bool>
{
    private readonly ApplicationDbContext _context;

    public CheckOrganizationMembershipHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<bool> Handle(CheckOrganizationMembershipQuery request, CancellationToken cancellationToken)
    {
        return await _context.Organizations
            .Where(o => o.Id == request.OrganizationId)
            .AnyAsync(o => o.Members.Any(m => m.UserProfileId == request.UserId), cancellationToken);
    }
}