﻿using CouponApp.Server.Data;
using CouponApp.Server.Models.Entities;
using Mediator;
using Microsoft.EntityFrameworkCore;

namespace CouponApp.Server.Features.LeadForms.Handlers;

public class GetLeadFormsByCampaignHandler : IRequestHandler<GetLeadFormsByCampaignQuery, IEnumerable<LeadForm>>
{
    private readonly ApplicationDbContext _context;

    public GetLeadFormsByCampaignHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<IEnumerable<LeadForm>> Handle(GetLeadFormsByCampaignQuery request, CancellationToken cancellationToken)
    {
        return await _context.LeadForms
            .Where(lf => lf.CampaignId == request.CampaignId)
            .OrderByDescending(lf => lf.CreatedAt)
            .ToListAsync(cancellationToken);
    }
}