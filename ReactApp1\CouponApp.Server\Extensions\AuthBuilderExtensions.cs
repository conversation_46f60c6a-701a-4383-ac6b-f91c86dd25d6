﻿using Logto.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;


namespace CouponApp.Server.Extensions;


public static class LogtoFixHelper
{


  public static void ConfigureCookieOptions(string authenticationScheme, CookieAuthenticationOptions options, LogtoOptions logtoOptions)
  {
    options.Cookie.Name = $"Logto.Cookie.{logtoOptions.AppId}";
    options.SlidingExpiration = true;
    options.Cookie.Domain = logtoOptions.CookieDomain;
    options.Events = new CookieAuthenticationEvents
    {
      OnValidatePrincipal = context => new LogtoCookieContextManager(authenticationScheme, context).Handle(),
      OnRedirectToLogin = context =>
      {
          context.Response.StatusCode = StatusCodes.Status401Unauthorized;
          return Task.CompletedTask;
      },
    };
  }

}