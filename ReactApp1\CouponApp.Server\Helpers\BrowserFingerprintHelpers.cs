﻿using System;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace CouponApp.Server.Helpers;

public class BrowserFingerprintHelpers
{
    public static JsonElement? DecryptFingerprint(string encryptedString)
    {
        try
        {
            var decryptedFingerprint = DecryptAndParse(encryptedString, "romanppczyk");
            return decryptedFingerprint;
        }
        catch (Exception)
        {
            return null;
        }
    }

    private static JsonElement DecryptAndParse(string encryptedHex, string password)
    {
        // Convert hex string to byte array
        byte[] encryptedBytes = HexStringToByteArray(encryptedHex);

        // Convert password to byte array
        byte[] passwordBytes = Encoding.UTF8.GetBytes(password);

        // XOR decryption
        byte[] decryptedBytes = new byte[encryptedBytes.Length];
        for (int i = 0; i < encryptedBytes.Length; i++)
        {
            decryptedBytes[i] = (byte) (encryptedBytes[i] ^ passwordBytes[i % passwordBytes.Length]);
        }

        // Convert decrypted bytes back to string
        var decryptedJson = Encoding.UTF8.GetString(decryptedBytes);

        // Parse JSON as JsonElement
        return JsonSerializer.Deserialize<JsonElement>(decryptedJson);
    }

    private static byte[] HexStringToByteArray(string hex)
    {
        int numberChars = hex.Length;
        byte[] bytes = new byte[numberChars / 2];
        for (int i = 0; i < numberChars; i += 2)
        {
            bytes[i / 2] = Convert.ToByte(hex.Substring(i, 2), 16);
        }

        return bytes;
    }
}