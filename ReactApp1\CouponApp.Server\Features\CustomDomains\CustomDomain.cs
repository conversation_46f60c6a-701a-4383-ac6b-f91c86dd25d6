﻿using System.ComponentModel.DataAnnotations.Schema;
using CouponApp.Server.Models.Enums;

namespace CouponApp.Server.Models.Entities;

public class CustomDomain : BaseEntity
{
    public Guid OrganizationId { get; set; }
    public string DomainName { get; set; }
    
    public DomainHostnameHandlerType DomainHostnameHandlerType { get; set; }
    
    public string? CloudflareHostnameId { get; set; }
    
    public CustomDomainStatus Status { get; set; }
    public string? ErrorMessage { get; set; }
    public bool HasValidSsl { get; set; }

    [ForeignKey("OrganizationId")]
    public Organization Organization { get; set; }
}

public enum DomainHostnameHandlerType
{
    Cloudflare = 0
}