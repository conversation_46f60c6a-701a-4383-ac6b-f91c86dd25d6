﻿using CouponApp.Server.Data;
using CouponApp.Server.Models.Entities;
using Mediator;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.LeadForms.Handlers;

public class SubmitLeadFormHandler : IRequestHandler<SubmitLeadFormCommand, OneOf<LeadForm, Error<string>>>
{
    private readonly ApplicationDbContext _context;

    public SubmitLeadFormHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<LeadForm, Error<string>>> <PERSON><PERSON>(SubmitLeadFormCommand request, CancellationToken cancellationToken)
    {
        var campaign = await _context.Campaigns.FindAsync(new object[] { request.SubmissionDto.CampaignId }, cancellationToken);
        if (campaign == null)
        {
            return new Error<string>("Campaign not found");
        }

        var leadForm = new LeadForm
        {
            CampaignId = request.SubmissionDto.CampaignId,
            FormData = request.SubmissionDto.FormData
        };

        _context.LeadForms.Add(leadForm);
        await _context.SaveChangesAsync(cancellationToken);

        return leadForm;
    }
}