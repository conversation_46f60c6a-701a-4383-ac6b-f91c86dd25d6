﻿// FILE: '~/CouponApp.Server/Handlers/LogtoAuthHandler.cs'

using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;

namespace CouponApp.Server.Handlers
{
    public class LogtoAuthHandler : DelegatingHandler
    {
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _cache;
        private readonly ILogger<LogtoAuthHandler> _logger;

        private const string CacheKey = "LogtoAccessToken";

        public LogtoAuthHandler(IConfiguration configuration, IMemoryCache cache, ILogger<LogtoAuthHandler> logger)
        {
            _configuration = configuration;
            _cache = cache;
            _logger = logger;
        }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            var accessToken = await GetAccessTokenAsync(cancellationToken);
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
            return await base.SendAsync(request, cancellationToken);
        }

        private async Task<string> GetAccessTokenAsync(CancellationToken cancellationToken)
        {
            if (_cache.TryGetValue(CacheKey, out string cachedToken))
            {
                return cachedToken;
            }

            var tokenEndpoint = _configuration["Logto:TokenEndpoint"];
            var clientId = _configuration["Logto:ClientId"];
            var clientSecret = _configuration["Logto:ClientSecret"];
            var resource = _configuration["Logto:ApiResource"];

            var client = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Post, tokenEndpoint)
            {
                Content = new FormUrlEncodedContent(new Dictionary<string, string>
                {
                    { "grant_type", "client_credentials" },
                    { "resource", resource },
                    { "scope", "all" }
                })
            };

            var basicAuthenticationValue = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{clientId}:{clientSecret}"));
            request.Headers.Authorization = new AuthenticationHeaderValue("Basic", basicAuthenticationValue);

            try
            {
                var response = await client.SendAsync(request, cancellationToken);
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(content);

                if (tokenResponse?.AccessToken == null)
                {
                    throw new Exception("Failed to obtain access token");
                }

                _cache.Set(CacheKey, tokenResponse.AccessToken, TimeSpan.FromSeconds(tokenResponse.ExpiresIn - 60));
                _logger.LogInformation("New Logto access token acquired and cached.");

                return tokenResponse.AccessToken;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to obtain Logto access token");
                throw;
            }
        }

        private class TokenResponse
        {
            [JsonPropertyName("access_token")]
            public string AccessToken { get; set; }

            [JsonPropertyName("expires_in")]
            public int ExpiresIn { get; set; }

            [JsonPropertyName("token_type")]
            public string TokenType { get; set; }

            [JsonPropertyName("scope")]
            public string Scope { get; set; }
        }
    }
}

// EOF: '~/CouponApp.Server/Handlers/LogtoAuthHandler.cs'