﻿using System.ComponentModel.DataAnnotations.Schema;
using CouponApp.Server.Data;

namespace CouponApp.Server.Models.Entities;

public class UserOrganization : BaseEntity
{
     public string UserProfileId { get; set; }
     public Guid OrganizationId { get; set; }
     public UserOrganizationRole Role { get; set; }
     
     [ForeignKey(nameof(UserProfileId))]
     public UserProfile UserProfile { get; set; }
     
     [ForeignKey(nameof(OrganizationId))]
     public Organization Organization { get; set; }
    
}

public enum UserOrganizationRole
{
    Member = 0,
    Owner = 1,
}