﻿// Controllers/CustomDomainsController.cs

using CouponApp.Server.Attributes;
using CouponApp.Server.Features.CustomDomains;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.DTOs.Requests;
using CouponApp.Server.Models.Enums;
using Mediator;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

[ApiController]
[Route("api/organizations/{organizationId}/domains")]
[Authorize]
public class CustomDomainsController : ControllerBase
{
    private readonly IMediator _mediator;

    public CustomDomainsController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpGet]
    [RequirePermission(PermissionType.OrganizationRead, "organizationId")]
    public async Task<ActionResult<List<CustomDomainDto>>> GetDomains(Guid organizationId)
    {
        var result = await _mediator.Send(new GetCustomDomainsQuery(organizationId));
        return Ok(result);
    }
    [HttpGet("active")]
    [RequirePermission(PermissionType.OrganizationRead, "organizationId")]
    public async Task<ActionResult<List<CustomDomainDto>>> GetActiveDomains(Guid organizationId)
    {
        var result = await _mediator.Send(new GetCustomDomainsQuery(organizationId));
        return Ok(result.Where(d => d.Status == CustomDomainStatus.Active).ToList());
    }

    [HttpPost]
    [RequirePermission(PermissionType.OrganizationWrite, "organizationId")]
    public async Task<ActionResult<CustomDomainDto>> CreateDomain(Guid organizationId, CreateCustomDomainRequest dto)
    {
        var result = await _mediator.Send(new CreateCustomDomainCommand(organizationId, dto));
        
        return result.Match<ActionResult<CustomDomainDto>>(
            domain => CreatedAtAction(nameof(GetDomain), new { organizationId, id = domain.Id }, domain),
            error => BadRequest(error.Value)
        );
    }

    [HttpGet("{id}")]
    [RequirePermission(PermissionType.OrganizationRead, "organizationId")]
    public async Task<ActionResult<CustomDomainDto>> GetDomain(Guid organizationId, Guid id)
    {
        var result = await _mediator.Send(new GetCustomDomainQuery(organizationId, id));
        
        return result.Match<ActionResult<CustomDomainDto>>(
            domain => Ok(domain),
            notFound => NotFound()
        );
    }

    [HttpDelete("{id}")]
    [RequirePermission(PermissionType.OrganizationWrite, "organizationId")]
    public async Task<IActionResult> DeleteDomain(Guid organizationId, Guid id)
    {
        var result = await _mediator.Send(new DeleteCustomDomainCommand(organizationId, id));
        
        return result.Match<IActionResult>(
            success => NoContent(),
            notFound => NotFound()
        );
    }

}