﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CouponApp.Server.Models.Entities;

public class CampaignUniqueEnter : BaseEntity
{
    [Required]
    public Guid CampaignAnalyticsId { get; set; }

    [ForeignKey("CampaignAnalyticsId")]
    public CampaignAnalytics CampaignAnalytics { get; set; }

    public string? IpAddress { get; set; }

    public string? BrowserFingerprint { get; set; }

    public string? LocalStorageValue { get; set; }

    [Required]
    public DateTime EnteredAt { get; set; }
}