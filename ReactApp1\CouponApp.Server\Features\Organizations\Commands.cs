﻿// File: ~/CouponApp.Server/Features/Organizations/Commands.cs

using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Models.Errors;
using CouponApp.Server.Services;
using CouponApp.Server.Services.Integrations;
using Mediator;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Organizations;

public record CreateOrganizationCommand(string UserId,  CreateOrganizationDto Dto) : IRequest<OneOf<OrganizationDto, OrganizationAlreadyExists, UserNotFound>>;
public record UpdateOrganizationCommand(Guid Id, UpdateOrganizationDto Dto) : IRequest<OneOf<OrganizationDto, NotFound>>;
public record DeleteOrganizationCommand(Guid Id) : IRequest<OneOf<Success, NotFound>>;
public record AddIntegrationCommand(AddOrganizationIntegrationDto Dto) : IRequest<OneOf<OrganizationIntegrationDto, NotFound, Error<string>>>;
public record DisconnectIntegrationCommand(Guid OrganizationId, IntegrationType IntegrationType) : IRequest<OneOf<Success, Error<string>>>;

