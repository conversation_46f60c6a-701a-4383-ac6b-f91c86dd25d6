﻿using System.Text.RegularExpressions;

namespace CouponApp.Server.Features.CustomDomains.Handlers;


using CouponApp.Server.Data;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Models.Enums;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

public class CreateCustomDomainHandler : IRequestHandler<CreateCustomDomainCommand, OneOf<CustomDomainDto, Error<string>>>
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<CreateCustomDomainHandler> _logger;

    private static readonly Regex DomainRegex = new(
        @"^(?!-)[A-Za-z0-9-]{1,63}(?<!-)(\.[A-Za-z0-9-]{1,63})*\.[A-Za-z]{2,}$", 
        RegexOptions.Compiled | RegexOptions.IgnoreCase);
    
    public CreateCustomDomainHandler(
        ApplicationDbContext context,
        ILogger<CreateCustomDomainHandler> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async ValueTask<OneOf<CustomDomainDto, Error<string>>> Handle(
        CreateCustomDomainCommand request, 
        CancellationToken cancellationToken)
    {
        // Validate domain name format
        if (!IsValidDomainName(request.Dto.DomainName))
        {
            return new Error<string>("Invalid domain name format");
        }

        // Check if domain already exists
        var domainExists = await _context.CustomDomains
            .AnyAsync(d => d.DomainName == request.Dto.DomainName, cancellationToken);

        if (domainExists)
        {
            return new Error<string>("Domain already exists");
        }

        // Check organization domain limit (e.g., max 5 domains per organization)
        var domainCount = await _context.CustomDomains
            .CountAsync(d => d.OrganizationId == request.OrganizationId, cancellationToken);

        if (domainCount >= 2)
        {
            return new Error<string>("Organization domain limit reached. Please subscribe to a higher tier plan.");
        }

        var domain = new CustomDomain
        {
            OrganizationId = request.OrganizationId,
            DomainName = request.Dto.DomainName.ToLowerInvariant(),
            Status = CustomDomainStatus.Initializing,
            DomainHostnameHandlerType = DomainHostnameHandlerType.Cloudflare
        };

        _context.CustomDomains.Add(domain);
        await _context.SaveChangesAsync(cancellationToken);

        return MapToDto(domain);
    }

    private bool IsValidDomainName(string domain)
    {
        if (string.IsNullOrWhiteSpace(domain) || domain.Length > 253)
        {
            return false;
        }

        return DomainRegex.IsMatch(domain);
    }

    private CustomDomainDto MapToDto(CustomDomain domain)
    {
        return new CustomDomainDto
        {
            Id = domain.Id,
            DomainName = domain.DomainName,
            Status = domain.Status,
            ErrorMessage = domain.ErrorMessage,
            HasValidSsl = domain.HasValidSsl,
        };
    }
}