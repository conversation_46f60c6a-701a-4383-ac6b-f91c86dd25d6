﻿using System.Security.Cryptography;
using System.Text;

namespace CouponApp.Server.Helpers;

public class HmacHelper
{
    public static string ComputeHmacSha256(string hmacKey, string message)
    {
        byte[] keyBytes = Encoding.UTF8.GetBytes(hmacKey);
        byte[] messageBytes = Encoding.UTF8.GetBytes(message);

        using (var hmac = new HMACSHA256(keyBytes))
        {
            byte[] hashBytes = hmac.ComputeHash(messageBytes);
            return BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
        }
    }
}