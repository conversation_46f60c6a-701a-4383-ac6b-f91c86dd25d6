﻿using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;

namespace CouponApp.Server.Mappers;

public static class OrganizationMapper
{
    public static OrganizationMiniDto ToMiniDto(this Organization organization)
    {
        return new OrganizationMiniDto
        {
            Id = organization.Id,
            Name = organization.Name,
            ShortId = organization.ShortId
        };
    }
    
    public static OrganizationDto ToDto(this Organization organization)
    {
        return new OrganizationDto
        {
            Id = organization.Id,
            ShortId = organization.ShortId,
            Name = organization.Name,
            LogoUrl = organization.LogoUrl,
            MemberCount = organization.Members.Count,
        };
    }
    
}