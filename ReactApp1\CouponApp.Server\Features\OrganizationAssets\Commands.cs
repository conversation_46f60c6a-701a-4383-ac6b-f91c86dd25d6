﻿using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.DTOs.Organization;
using Mediator;
using OneOf;
using OneOf.Types;
using CouponApp.Server.Models.Errors;

namespace CouponApp.Server.Features.OrganizationAssets;

public record UploadOrganizationAssetCommand(Guid OrganizationId, string UserId, IFormFile File) 
    : IRequest<OneOf<OrganizationAssetDto, Error<string>, UnauthorizedError, NotFound>>;

public record DeleteOrganizationAssetCommand(Guid OrganizationId, string UserId, Guid AssetId) 
    : IRequest<OneOf<Success, NotFound, UnauthorizedError>>;