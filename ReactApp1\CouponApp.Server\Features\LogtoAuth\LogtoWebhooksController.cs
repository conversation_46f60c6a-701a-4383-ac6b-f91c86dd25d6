﻿using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using CouponApp.Server.Features.Users;
using CouponApp.Server.Models.DTOs.Logto;
using Mediator;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CouponApp.Server.Controllers;

[ApiController]
[Route("api/auth/webhooks/logto")]
[Authorize]
public class LogtoWebhooksController : ControllerBase
{
    private readonly IMediator _mediator;

    public LogtoWebhooksController(IMediator mediator)
    {
        _mediator = mediator;
    }


    [HttpPost("user-created")]
    [AllowAnonymous]
    public async Task<IActionResult> CompleteSignIn()
    {
        // Replace "your_webhook_secret" with your actual secret
        var secret = "ypY0cqsZUYeW9DJ4jhxdC7QlllV99dDU";
        var signature = Request.Headers["logto-signature-sha-256"].ToString();

        // Read the request body as a string
        using var reader = new StreamReader(Request.Body);
        var requestBody = await reader.ReadToEndAsync();

        // Compute the hash
        using var hasher = new HMACSHA256(Encoding.UTF8.GetBytes(secret));
        var hash = hasher.ComputeHash(Encoding.UTF8.GetBytes(requestBody));
        var computedSignature = Convert.ToHexString(hash);

        // Compare signatures
        if (!signature.Equals(computedSignature, StringComparison.OrdinalIgnoreCase))
        {
            return Unauthorized("Invalid signature.");
        }

        var options = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        };

        var webhookRequest = JsonSerializer.Deserialize<LogtoWebhookRequest>(requestBody, options);
        if (webhookRequest == null)
        {
            return BadRequest("Failed to validate request body.");
        }

        if (webhookRequest.Event == "PostRegister")
        {
            await _mediator.Send(new CreateUserCommand(webhookRequest.User));
        }

        if (webhookRequest.Event == "PostSignIn")
        {
            await _mediator.Send(new CreateUserCommand(webhookRequest.User));
        }
        
        if (webhookRequest.Event == "User.Data.Updated")
        {
            var id = webhookRequest.Data.GetProperty("id").GetString();
            var newName = webhookRequest.Data.GetProperty("name").GetString();
            var newPrimaryEmail = webhookRequest.Data.GetProperty("primaryEmail").GetString();
            var newPrimaryPhone = webhookRequest.Data.GetProperty("primaryPhone").GetString();
            await _mediator.Send(new UpdateUserProfileCommand(id, newName, newPrimaryEmail, newPrimaryPhone));
            
        }

        Console.WriteLine("test");


        return Ok("Thanks");
    }
}