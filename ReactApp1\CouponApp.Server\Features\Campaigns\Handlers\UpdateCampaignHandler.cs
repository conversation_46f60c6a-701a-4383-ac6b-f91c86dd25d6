﻿using CouponApp.Server.Data;
using CouponApp.Server.Features.Campaigns;
using CouponApp.Server.Mappers;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Models.Enums;
using CouponApp.Server.Services;
using Mediator;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Campaigns.Handlers;

public class UpdateCampaignHandler : IRequestHandler<UpdateCampaignCommand, OneOf<CampaignEditorDto, NotFound, Error<string>>>
{
    private readonly ApplicationDbContext _context;

    public UpdateCampaignHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<CampaignEditorDto, NotFound, Error<string>>> Handle(UpdateCampaignCommand request,
        CancellationToken cancellationToken)
    {
        var campaign = await _context.Campaigns
            .Include(c => c.CouponPacks)
            .Include(c => c.CampaignDomainSettings)
            .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

        if (campaign == null)
        {
            return new NotFound();
        }

        if (request.UpdateDto.CampaignDomainSettings != null && !string.IsNullOrEmpty(request.UpdateDto.CampaignDomainSettings?.CustomDomainId))
        {
            var isDomainSettingsUsed = await _context.CampaignDomainSettings
                .Include(s => s.Campaign)
                .FirstOrDefaultAsync(
                    s => s.CampaignId != request.Id &&
                         s.CustomDomainId.ToString() == request.UpdateDto.CampaignDomainSettings.CustomDomainId &&
                         s.Slug == request.UpdateDto.CampaignDomainSettings.Slug,
                    cancellationToken: cancellationToken);

            var isDomainWithinOrganization = await _context.CustomDomains
                .AnyAsync(s =>
                    campaign.WorkspaceId == s.OrganizationId &&
                    s.Id.ToString() == request.UpdateDto.CampaignDomainSettings.CustomDomainId);

            if (isDomainSettingsUsed != null)
            {
                return new Error<string>("This slug is already used by another campaign: " +
                                         isDomainSettingsUsed.Campaign.Name);
            }

            if (!isDomainWithinOrganization)
            {
                return new Error<string>("This domain is not added to organization this campaign belongs to.");
            }
        }

        switch (request.UpdateDto.SaveMode)
        {
            case CampaignSaveMode.SaveAsDraft:
                campaign.DraftConfig = request.UpdateDto.Config;
                break;

            case CampaignSaveMode.SaveAndPublish:
                campaign.Config = request.UpdateDto.Config;
                campaign.IsPublished = true;
                campaign.DraftConfig = null;
                break;
        }

        campaign.UpdatedAt = DateTime.UtcNow;
        campaign.Name = request.UpdateDto.Name ?? campaign.Name;

        if (request.UpdateDto.CampaignDomainSettings != null && !string.IsNullOrEmpty(request.UpdateDto.CampaignDomainSettings?.CustomDomainId))
        {
            var customDomain = await _context.CustomDomains.FirstOrDefaultAsync(s =>
                s.Id.ToString() == request.UpdateDto.CampaignDomainSettings.CustomDomainId);

            if (campaign.CampaignDomainSettings == null)
            {
                campaign.CampaignDomainSettings = new CampaignDomainSettings
                {
                    CampaignId = campaign.Id,
                    Slug = request.UpdateDto.CampaignDomainSettings?.Slug,
                    CustomDomain = customDomain
                };
            }
            else
            {
                campaign.CampaignDomainSettings.CustomDomain = customDomain;
                campaign.CampaignDomainSettings.Slug = request.UpdateDto.CampaignDomainSettings?.Slug;
            }
        }
        else if (campaign.CampaignDomainSettings != null)
        {
             _context.CampaignDomainSettings.Remove(campaign.CampaignDomainSettings);
             campaign.CampaignDomainSettings = null;
             campaign.CampaignDomainSettingsId = null;
        }

        await ValidateCouponPacksAsync(campaign); //TODO: Move this to a notification handler instead.
        await _context.SaveChangesAsync(cancellationToken);
        
        return campaign.ToEditorDto();
    }

    private async Task ValidateCouponPacksAsync(Campaign campaign)
    {
        var couponPackIds = campaign.CouponPacks.Select(cp => cp.Id).ToList();
        campaign.HasNoCouponsLeft = false;

        if (couponPackIds.Count <= 0)
        {
            return;
        }

        var couponCounts = await _context.CouponPacks
            .Where(cp => couponPackIds.Contains(cp.Id))
            .Where(cp => cp.InterruptThreshold != null)
            .Select(cp => new
            {
                cp.Id,
                cp.Name,
                cp.NotifyThreshold,
                cp.InterruptThreshold,
                RemainingCoupons = cp.Coupons.Count()
            })
            .ToListAsync();


        if (couponCounts.Count <= 0)
        {
            return;
        }

        bool hasAvailableCoupons = false;

        foreach (var couponPack in couponCounts)
        {
            if (couponPack.RemainingCoupons > couponPack.InterruptThreshold)
            {
                hasAvailableCoupons = true;
            }
        }

        campaign.HasNoCouponsLeft = !hasAvailableCoupons;
    }
}