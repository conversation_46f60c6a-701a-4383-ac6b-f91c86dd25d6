﻿// File: ~/CouponApp.Server/Controllers/InvitationsController.cs

using Microsoft.AspNetCore.Mvc;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Extensions;
using Microsoft.AspNetCore.Authorization;
using CouponApp.Server.Features.Invitations;
using Mediator;

namespace CouponApp.Server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class InvitationsController : ControllerBase
    {
        private readonly IMediator _mediator;

        public InvitationsController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpPost("accept")]
        [Authorize]
        public async Task<IActionResult> AcceptInvitation([FromBody] AcceptInvitationDto dto)
        {
            var userId = User.GetId();
            var result = await _mediator.Send(new AcceptInvitationCommand(dto.Token, userId));

            return result.Match<IActionResult>(
                success => Ok(new { message = "Invitation accepted successfully" }),
                notFound => NotFound(),
                invalidInvitation => BadRequest(invalidInvitation.Message)
            );
        }
        
        
        [HttpDelete("{id}")]
        [Authorize]
        public async Task<IActionResult> DeleteInvitation(Guid id)
        {
            var result = await _mediator.Send(new DeleteInvitationCommand(id));

            return result.Match<IActionResult>(
                success => Ok(new { message = "Invitation accepted successfully" }),
                notFound => NotFound(),
                invalidInvitation => BadRequest(invalidInvitation.Message)
            );
        }

        [HttpGet("{token}")]
        [AllowAnonymous]
        public async Task<ActionResult<InvitationDetailsDto>> GetInvitationDetails(string token)
        {
            var result = await _mediator.Send(new GetInvitationDetailsQuery(token));

            return result.Match<ActionResult<InvitationDetailsDto>>(
                details => Ok(details),
                notFound => NotFound(),
                invalidInvitation => BadRequest(invalidInvitation.Message)
            );
        }
        
        [HttpPost]
        public async Task<IActionResult> InviteUser([FromBody] InviteUserDto dto)
        {
            var command = new CreateInvitationCommand(dto.OrganizationId, dto.Email, User.GetId());
            var result = await _mediator.Send(command);

            return result.Match<IActionResult>(
                invitation => Ok(invitation),
                notFound => NotFound(),
                alreadyInvited => BadRequest(alreadyInvited.Message),
                error => BadRequest(error.Value)
            );
        }
    }
}