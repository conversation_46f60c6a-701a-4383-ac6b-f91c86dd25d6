﻿using CouponApp.Server.Data;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Services;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Invitations.Handlers;

public class GetInvitationDetailsHandler : IRequestHandler<GetInvitationDetailsQuery, OneOf<InvitationDetailsDto, NotFound, InvalidInvitation>>
{
    private readonly ApplicationDbContext _context;

    public GetInvitationDetailsHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<InvitationDetailsDto, NotFound, InvalidInvitation>> Handle(GetInvitationDetailsQuery request, CancellationToken cancellationToken)
    {
        var invitation = await _context.Invitations
            .Include(i => i.Organization)
            .Include(i => i.InvitedByUser)
            .FirstOrDefaultAsync(i => i.InvitationToken == request.Token, cancellationToken);

        if (invitation == null)
        {
            return new NotFound();
        }

        if (invitation.ExpiresAt < DateTime.UtcNow)
        {
            return new InvalidInvitation("Invitation has expired");
        }

        return new InvitationDetailsDto
        {
            OrganizationName = invitation.Organization.Name,
            InviterEmail = invitation.InvitedByUser.FullName ?? invitation.InvitedByUser.Email ?? "Unknown", 
            OrganizationId = invitation.OrganizationId.ToString(),
            OrganizationShortId = invitation.Organization.ShortId
        };
    }
}