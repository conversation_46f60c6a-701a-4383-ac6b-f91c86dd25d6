﻿using System.Text.Json;
using Microsoft.Extensions.Caching.Distributed;

namespace CouponApp.Server.Extensions;

public static class DistributedCacheExtensions
{

    public async static Task<T?> GetFromJsonAsync<T>(this IDistributedCache cache, string key)
    {
        var json = await cache.GetStringAsync(key);
        if (json == null) return default;
        return JsonSerializer.Deserialize<T>(json);
    }    
    
    public async static Task SetJsonAsync(this IDistributedCache cache, string key, object value, DistributedCacheEntryOptions? options = null)
    {
         await cache.SetStringAsync(key, JsonSerializer.Serialize(value), options);
    }   
    
    public async static Task SetJsonAsync(this IDistributedCache cache, string key, object value, TimeSpan? options = null)
    {
         await cache.SetJsonAsync(key, value, new DistributedCacheEntryOptions
         {
             AbsoluteExpirationRelativeToNow = options
         });
    }
    
    public static async Task<bool> GetOrSetStringAsync(
        this IDistributedCache cache,
        string key,
        string value,
        DistributedCacheEntryOptions options,
        bool onlyIfNotExists = true)
    {
        try
        {
            if (onlyIfNotExists)
            {
                var existing = await cache.GetAsync(key);
                if (existing != null)
                {
                    return false;
                }
            }

            await cache.SetStringAsync(key, value, options);
            return true;
        }
        catch
        {
            return false;
        }
    }

}