﻿using CouponApp.Server.Features.CouponPacks;
using Mediator;

namespace CouponApp.Server.Features.Test.Handlers;

public class TestNotificationHandler:  INotificationHandler<TestNotification>
{
    public async ValueTask Handle(TestNotification notification, CancellationToken cancellationToken)
    {
        Console.WriteLine("Notification start");
        await Task.Delay(5000);
        Console.WriteLine("Notification end");
    }
}