﻿using CouponApp.Server.Data;
using CouponApp.Server.Features.Organizations;
using CouponApp.Server.Mappers;
using CouponApp.Server.Models.DTOs;
using Mediator;
using OneOf;
using OneOf.Types;

public class UpdateOrganizationHandler : IRequestHandler<UpdateOrganizationCommand, OneOf<OrganizationDto, NotFound>>
{
    private readonly ApplicationDbContext _context;

    public UpdateOrganizationHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<OrganizationDto, NotFound>> Handle(UpdateOrganizationCommand request, CancellationToken cancellationToken)
    {
        var organization = await _context.Organizations.FindAsync(new object[] { request.Id }, cancellationToken);
        if (organization == null)
        {
            return new NotFound();
        }

        organization.Name = request.Dto.Name;
        organization.LogoUrl = request.Dto.LogoUrl;

        await _context.SaveChangesAsync(cancellationToken);

        return organization.ToDto();
    }
}