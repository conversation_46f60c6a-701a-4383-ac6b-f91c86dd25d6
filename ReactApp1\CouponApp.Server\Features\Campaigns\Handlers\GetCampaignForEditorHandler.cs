﻿using CouponApp.Server.Data;
using CouponApp.Server.Mappers;
using CouponApp.Server.Models.DTOs;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Campaigns.Handlers;

public class GetCampaignForEditorHandler : IRequestHandler<GetCampaignForEditorQuery, OneOf<CampaignEditorDto, NotFound>>
{
    private readonly ApplicationDbContext _context;

    public GetCampaignForEditorHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<CampaignEditorDto, NotFound>> Handle(GetCampaignForEditorQuery request, CancellationToken cancellationToken)
    {
        var campaign = await _context.Campaigns
            .Include(c => c.CampaignDomainSettings)
            .AsSplitQuery()
            .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

        if (campaign is null)
        {
            return new NotFound();
        }

        return campaign.ToEditorDto();
    }
}