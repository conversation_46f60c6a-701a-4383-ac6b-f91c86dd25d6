using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using CouponApp.Server.Data;
using CouponApp.Server.Models.DTOs;

namespace CouponApp.Server.Models.Entities;

public class Campaign : BaseEntity
{

    public string CreatedByUserId { get; set; }
    
    public bool IsPublished { get; set; }
    
    [Column(TypeName = "jsonb")]
    public CampaignConfigDto Config { get; set; }
    
    [Column(TypeName = "jsonb")]
    public CampaignConfigDto? DraftConfig { get; set; }
    
    public List<CouponPack> CouponPacks { get; set; } = new List<CouponPack>();
    
    public Guid WorkspaceId { get; set; }

    [ForeignKey("WorkspaceId")]
    public Organization Workspace { get; set; }
    
    
    public Guid? CampaignAnalyticsId { get; set; }
    
    [ForeignKey("CampaignAnalyticsId")]
    public CampaignAnalytics? CampaignAnalytics {get;set; }
    
    public string Name { get; set; }

    
    public bool? HasNoCouponsLeft {get; set;}
    
    
     public Guid? CampaignDomainSettingsId { get; set; }
    
     [ForeignKey("CampaignDomainSettingsId")]
     public CampaignDomainSettings? CampaignDomainSettings { get; set; }
}



public class CampaignDomainSettings : BaseEntity 
{
    public Guid CampaignId { get; set; }
    public Guid CustomDomainId { get; set; }
    public string? Slug { get; set; }

    [ForeignKey("CampaignId")]
    public Campaign Campaign { get; set; }
    
    [ForeignKey("CustomDomainId")]
    public CustomDomain CustomDomain { get; set; }
    
}

public class CampaignConfigDto
{
    [Column(TypeName = "jsonb")]
    public List<object> Scenes { get; set; }
    
        
    [Column(TypeName = "jsonb")]
    public CampaignFlow[] Flows { get; set; }
    
    
    [Column(TypeName = "jsonb")]
    public object Variables { get; set; }

    public int Revision { get; set; }
    
    public List<string> UsedAssetIds { get; set; }

}

public record CampaignFlow
{
    public string Id { get; set; }
    public string Name { get; set; }
    
    [Column(TypeName = "jsonb")]
    public CampaignFlowNode[] Nodes { get; set; }
}

public record CampaignFlowNode
{
    public string Id { get; set; }
    public string Type { get; set; }
    public Point Position { get; set; }
    public List<string> Connections { get; set; }
    public string? ElseConnection { get; set; }
    public Dictionary<string, object>? Payload { get; set; }

}

public record Point
{
    public int X { get; set; }
    public int Y { get; set; }
}
