﻿namespace CouponApp.Server.Models;

public class CampaignNodeType
{
   public const string OnFirstLoad = "rigger:OnFirstLoad";
   public const string OnButtonPress = "trigger:OnButtonPress";
   public const string OnGameFinishNoReward = "trigger:OnGameFinishNoReward";
   public const string OnGameFinishWithReward = "trigger:OnGameFinishWithReward";
   public const string SetScene = "client:SetScene";
   public const string Webhook = "server:Webhook";
   public const string AddContactToMailchimpList = "server:AddContactToMailchimpList";
   public const string SendFormToHubspot = "server:SendFormToHubspot";
   public const string AddCustomerToShopify = "server:AddCustomerToShopify";
}