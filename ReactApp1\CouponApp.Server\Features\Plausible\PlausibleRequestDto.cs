﻿namespace CouponApp.Server.Models.DTOs.Plausible;

using System.Text.Json.Serialization;

public class PlausibleRequestDto
{
    [JsonPropertyName("site_id")] public string SiteId { get; set; }

    [JsonPropertyName("metrics")] public List<string> Metrics { get; set; }

    [JsonPropertyName("date_range")] public string DateRange { get; set; }

    [JsonPropertyName("dimensions")] public List<string> Dimensions { get; set; }

    [JsonPropertyName("filters")] public List<List<object>> Filters { get; set; }
}

public class PlausibleResponseDto
{
    [JsonPropertyName("results")] public List<PlausibleResultItemDto> Results { get; set; }

    [JsonPropertyName("meta")] public object Meta { get; set; }

    [JsonPropertyName("query")] public PlausibleQueryDto Query { get; set; }
}

public class PlausibleResultItemDto
{
    [JsonPropertyName("metrics")] public List<int> Metrics { get; set; }

    [JsonPropertyName("dimensions")] public List<string> Dimensions { get; set; }
}

public class PlausibleQueryDto
{
    [JsonPropertyName("site_id")] public string SiteId { get; set; }

    [JsonPropertyName("metrics")] public List<string> Metrics { get; set; }

    [JsonPropertyName("date_range")] public List<string> DateRange { get; set; }

    [JsonPropertyName("filters")] public List<List<object>> Filters { get; set; }

    [JsonPropertyName("dimensions")] public List<string> Dimensions { get; set; }

    [JsonPropertyName("order_by")] public List<List<string>> OrderBy { get; set; }

    [JsonPropertyName("include")] public object Include { get; set; }

    [JsonPropertyName("pagination")] public PaginationDto Pagination { get; set; }
}

public class PaginationDto
{
    [JsonPropertyName("offset")] public int Offset { get; set; }

    [JsonPropertyName("limit")] public int Limit { get; set; }
}