﻿using CouponApp.Server.Data;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Models.Errors;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;
using Minio;
using Minio.DataModel.Args;

namespace CouponApp.Server.Features.OrganizationAssets.Handlers;

public class DeleteOrganizationAssetHandler
    : IRequestHandler<DeleteOrganizationAssetCommand, OneOf<Success, NotFound, UnauthorizedError>>
{
    private readonly ApplicationDbContext _context;
    private readonly IMinioClient _minioClient;
    private readonly IConfiguration _configuration;

    public DeleteOrganizationAssetHandler(ApplicationDbContext context, IMinioClient minioClient, IConfiguration configuration)
    {
        _context = context;
        _minioClient = minioClient;
        _configuration = configuration;
    }

    public async ValueTask<OneOf<Success, NotFound, UnauthorizedError>> Handle(
        DeleteOrganizationAssetCommand request, CancellationToken cancellationToken)
    {
        var asset = await _context.OrganizationAssets
            .Include(a => a.Organization)
            .ThenInclude(o => o.Members)
            .FirstOrDefaultAsync(a => a.Id == request.AssetId, cancellationToken);

        if (asset == null)
            return new NotFound();

        if (!asset.Organization.Members.Any(m => m.UserProfileId == request.UserId))
        {
            return new UnauthorizedError("You are not a member of this organization");
        }

        var bucketName = _configuration["Minio:BucketName"];
        var minioPublicUrl = _configuration["Minio:PublicUrl"];
        var objectName = asset.FileUrl.Replace($"{minioPublicUrl}/{bucketName}/", "");

        await _minioClient.RemoveObjectAsync(new RemoveObjectArgs()
            .WithBucket(bucketName)
            .WithObject(objectName), cancellationToken);

        _context.OrganizationAssets.Remove(asset);
        await _context.SaveChangesAsync(cancellationToken);

        return new Success();
    }
}