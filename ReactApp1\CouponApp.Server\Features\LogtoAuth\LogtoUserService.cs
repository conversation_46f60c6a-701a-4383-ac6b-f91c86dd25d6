﻿using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Caching.Memory;

namespace CouponApp.Server.Services
{
    public interface ILogtoUserService
    {
        Task<bool> UpdateUserProfile(string userId, string firstName, string lastName);
        Task<bool> UpdateUserEmail(string userId, string email);
        Task<bool> UpdateUserPassword(string userId, string newPassword);
        Task<bool> CheckIfUserHasPassword(string userId);
    }

    public class LogtoUserService : ILogtoUserService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _cache;
        private const string AccessTokenCacheKey = "LogtoAccessToken";

        public LogtoUserService(IHttpClientFactory httpClientFactory, IConfiguration configuration, IMemoryCache cache)
        {
            _httpClient = httpClientFactory.CreateClient();
            _configuration = configuration;
            _cache = cache;
        }

        private async Task<string> GetAccessTokenAsync()
        {
            if (_cache.TryGetValue(AccessTokenCacheKey, out string cachedToken))
            {
                return cachedToken;
            }

            var tokenEndpoint = $"{_configuration["Logto:Endpoint"]}/oidc/token";
            var clientId = _configuration["Logto:ClientId"];
            var clientSecret = _configuration["Logto:ClientSecret"];
            var resource = $"{_configuration["Logto:Endpoint"]}/api";

            var request = new HttpRequestMessage(HttpMethod.Post, tokenEndpoint)
            {
                Content = new FormUrlEncodedContent(new Dictionary<string, string>
                {
                    {"grant_type", "client_credentials"},
                    {"resource", resource},
                    {"scope", "all"}
                })
            };

            request.Headers.Authorization = new AuthenticationHeaderValue(
                "Basic",
                Convert.ToBase64String(Encoding.ASCII.GetBytes($"{clientId}:{clientSecret}"))
            );

            var response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync();
            var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(content);

            if (tokenResponse?.AccessToken == null)
            {
                throw new Exception("Failed to obtain access token");
            }

            _cache.Set(AccessTokenCacheKey, tokenResponse.AccessToken, TimeSpan.FromSeconds(tokenResponse.ExpiresIn - 60));

            return tokenResponse.AccessToken;
        }

        private async Task<HttpResponseMessage> SendAuthenticatedRequestAsync(HttpMethod method, string endpoint, HttpContent content = null)
        {
            var accessToken = await GetAccessTokenAsync();
            var request = new HttpRequestMessage(method, $"{_configuration["Logto:Endpoint"]}/api{endpoint}");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
            
            if (content != null)
            {
                request.Content = content;
            }

            return await _httpClient.SendAsync(request);
        }

        public async Task<bool> UpdateUserProfile(string userId, string firstName, string lastName)
        {
            var updateData = new { name = $"{firstName} {lastName}" };
            var content = new StringContent(JsonSerializer.Serialize(updateData), Encoding.UTF8, "application/json");
            var response = await SendAuthenticatedRequestAsync(HttpMethod.Patch, $"/users/{userId}", content);
            return response.IsSuccessStatusCode;
        }

        public async Task<bool> UpdateUserEmail(string userId, string email)
        {
            var updateData = new { primaryEmail = email };
            var content = new StringContent(JsonSerializer.Serialize(updateData), Encoding.UTF8, "application/json");
            var response = await SendAuthenticatedRequestAsync(HttpMethod.Patch, $"/users/{userId}", content);
            return response.IsSuccessStatusCode;
        }

        public async Task<bool> UpdateUserPassword(string userId, string newPassword)
        {
            var updateData = new { password = newPassword };
            var content = new StringContent(JsonSerializer.Serialize(updateData), Encoding.UTF8, "application/json");
            var response = await SendAuthenticatedRequestAsync(HttpMethod.Patch, $"/users/{userId}/password", content);
            return response.IsSuccessStatusCode;
        }

        public async Task<bool> CheckIfUserHasPassword(string userId)
        {
            var response = await SendAuthenticatedRequestAsync(HttpMethod.Get, $"/users/{userId}/has-password");
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<HasPasswordResponse>(content);
                return result?.HasPassword ?? false;
            }
            return false;
        }
    }

    internal class TokenResponse
    {
        [JsonPropertyName("access_token")]
        public string AccessToken { get; set; }

        [JsonPropertyName("expires_in")]
        public int ExpiresIn { get; set; }

        [JsonPropertyName("token_type")]
        public string TokenType { get; set; }

        [JsonPropertyName("scope")]
        public string Scope { get; set; }
    }

    internal class HasPasswordResponse
    {
        [JsonPropertyName("hasPassword")]
        public bool HasPassword { get; set; }
    }
}