﻿using Microsoft.AspNetCore.Mvc;
using Mediator;
using CouponApp.Server.Features.FileUpload;
using Microsoft.AspNetCore.Authorization;

namespace CouponApp.Server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class FileUploadController : ControllerBase
    {
        private readonly IMediator _mediator;

        public FileUploadController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpPost]
        public async Task<IActionResult> UploadFile(IFormFile file)
        {
            var result = await _mediator.Send(new UploadFileCommand(file));
            return result.Match<IActionResult>(
                fileUrl => Ok(new {Url = fileUrl}),
                error => BadRequest(error));
        }
    }
}