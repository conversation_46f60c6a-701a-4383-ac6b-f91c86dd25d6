﻿using CouponApp.Server.Models.Entities;
using CouponApp.Server.Services;

namespace CouponApp.Server.Features.CustomDomains.Handlers;


using CouponApp.Server.Data;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;
public class DeleteCustomDomainHandler : IRequestHandler<DeleteCustomDomainCommand, OneOf<Success, NotFound>>
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<DeleteCustomDomainHandler> _logger;
    private readonly ICloudflareService _cloudflareService;

    public DeleteCustomDomainHandler(
        ApplicationDbContext context,
        ILogger<DeleteCustomDomainHandler> logger, ICloudflareService cloudflareService)
    {
        _context = context;
        _logger = logger;
        _cloudflareService = cloudflareService;
    }

    public async ValueTask<OneOf<Success, NotFound>> Handle(
        DeleteCustomDomainCommand request, 
        CancellationToken cancellationToken)
    {
        var domain = await _context.CustomDomains
            .FirstOrDefaultAsync(d => 
                    d.Id == request.DomainId && 
                    d.OrganizationId == request.OrganizationId, 
                cancellationToken);

        if (domain == null)
        {
            return new NotFound();
        }

         // If using Cloudflare, delete the hostname first
         if (domain.CloudflareHostnameId != null && domain.DomainHostnameHandlerType == DomainHostnameHandlerType.Cloudflare)
         {
             try
             {
                  await _cloudflareService.DeleteHostnameAsync(domain.CloudflareHostnameId);
             }
             catch (Exception ex)
             {
                 _logger.LogError(ex, "Failed to delete Cloudflare hostname for domain {DomainName}", domain.DomainName);
             }
         }

         //TODO: This should be a notification and within it we should delete the domain!
         await _context.Campaigns
             .Where(c => c.CampaignDomainSettings.CustomDomainId == domain.Id)
             .ExecuteUpdateAsync(setters => setters.SetProperty(campaign => campaign.CampaignDomainSettingsId,  c => null));
         
         await _context.CampaignDomainSettings.Where(cds => cds.CustomDomainId == domain.Id).ExecuteDeleteAsync();

        _context.CustomDomains.Remove(domain);
        await _context.SaveChangesAsync(cancellationToken);

        return new Success();
    }
}