﻿// File: ~/CouponApp.Server/Features/Newsletter/Handlers/CheckSubscriptionStatusHandler.cs

using CouponApp.Server.Data;
using CouponApp.Server.Features.Campaigns;
using CouponApp.Server.Features.Mailchimp;
using CouponApp.Server.Features.Organizations;
using CouponApp.Server.Services.Integrations;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Newsletter.Handlers;

public class CheckSubscriptionStatusHandler : IRequestHandler<CheckSubscriptionStatusQuery, OneOf<bool, NotFound, Error<string>>>
{
    private readonly IMediator _mediator;
    private readonly ApplicationDbContext _context;

    public CheckSubscriptionStatusHandler(IMediator mediator, ApplicationDbContext context)
    {
        _mediator = mediator;
        _context = context;
    }

    public async ValueTask<OneOf<bool, NotFound, Error<string>>> Handle(CheckSubscriptionStatusQuery request, CancellationToken cancellationToken)
    {
        var campaign = await _context.Campaigns.FirstOrDefaultAsync(c => c.Id == request.CampaignId, cancellationToken);
        if (campaign == null)
        {
            return new Error<string>("Campaign not found");
        }

        var integrationQuery = new GetIntegrationByIdQuery(campaign.WorkspaceId, IntegrationType.Mailchimp);
        var integrationResult = await _mediator.Send(integrationQuery);
        if (!integrationResult.TryPickT0(out var integration, out var _))
        {
            return new Error<string>("Mailchimp integration not found for this organization");
        }

        var searchMemberQuery = new SearchMailchimpMemberQuery(integration.ConnectionId, request.ListId, request.Email);
        var searchMemberResult = await _mediator.Send(searchMemberQuery);

        return searchMemberResult.Match<OneOf<bool, NotFound, Error<string>>>(
            success => success.ExactMatches.Members.Any(),
            error => new Error<string>(error.Value)
        );
    }
}