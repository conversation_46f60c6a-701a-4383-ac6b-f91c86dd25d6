﻿using CouponApp.Server.Data;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Services;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Invitations.Handlers;

public class DeleteInvitationHandler : IRequestHandler<DeleteInvitationCommand, OneOf<Success, NotFound, InvalidInvitation>>
{
    private readonly ApplicationDbContext _context;

    public DeleteInvitationHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<Success, NotFound, InvalidInvitation>> Handle(DeleteInvitationCommand request, CancellationToken cancellationToken)
    {
        var invitation = await _context.Invitations
            .FirstOrDefaultAsync(i => i.Id == request.Id, cancellationToken);

        if (invitation == null)
        {
            return new NotFound();
        }
        
        _context.Invitations.Remove(invitation);
        await _context.SaveChangesAsync(cancellationToken);
        return new Success();
    }
}
