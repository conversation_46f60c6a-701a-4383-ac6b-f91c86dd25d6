﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CouponApp.Server.Data;

namespace CouponApp.Server.Models.Entities;

public class Invitation : BaseEntity
{
    [Required]
    public string Email { get; set; }
    
    [Required]
    public Guid OrganizationId { get; set; }
    
    public string InvitationToken { get; set; }
    
    public DateTime ExpiresAt { get; set; }
    
    public string InvitedByUserId { get; set; }
    
    
    [ForeignKey(nameof(InvitedByUserId))]
    public UserProfile InvitedByUser { get; set; }
    
    [ForeignKey(nameof(OrganizationId))]
    public Organization Organization { get; set; }
}