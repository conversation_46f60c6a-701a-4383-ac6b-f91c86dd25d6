﻿// File: Controllers/OrganizationCampaignsController.cs
using Microsoft.AspNetCore.Mvc;
using CouponApp.Server.Features.Campaigns;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Attributes;
using CouponApp.Server.Extensions;
using Mediator;
using Microsoft.AspNetCore.Authorization;

namespace CouponApp.Server.Controllers
{
    [ApiController]
    [Route("api/organizations/{workspaceId}/campaigns")]
    [Authorize]
    public class OrganizationCampaignsController : ControllerBase
    {
        private readonly IMediator _mediator;

        public OrganizationCampaignsController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpGet]
        [RequirePermission(PermissionType.OrganizationRead, "workspaceId")]
        public async Task<ActionResult<List<CampaignMiniDto>>> GetCampaigns(Guid workspaceId)
        {
            var result = await _mediator.Send(new GetCampaignsByOrganizationIdQuery(workspaceId));

            return result.Match<ActionResult<List<CampaignMiniDto>>>(
                campaigns => Ok(campaigns),
                notFound => NotFound("Organization not found")
            );
        }

        [HttpPost]
        [RequirePermission(PermissionType.OrganizationWrite, "workspaceId")]
        public async Task<ActionResult<CampaignEditorDto>> CreateCampaign(Guid workspaceId, [FromBody] CampaignCreateDto campaignCreateDto)
        {
            var userId = User.GetId();
            var result = await _mediator.Send(new CreateCampaignCommand(campaignCreateDto, userId));

            return result.Match<ActionResult<CampaignEditorDto>>(
                campaign => Ok(new { id = campaign.Id, name = campaign.Name, slug = campaign.Slug }),
                error => BadRequest(error.Value)
            );
        }

        [HttpPut("{id:guid}")]
        [RequirePermission(PermissionType.CampaignWrite, "id")]
        public async Task<ActionResult<CampaignEditorDto>> UpdateCampaign(Guid workspaceId, Guid id, [FromBody] CampaignUpdateDto campaignUpdateDto)
        {
            var result = await _mediator.Send(new UpdateCampaignCommand(id, campaignUpdateDto));

            return result.Match<ActionResult<CampaignEditorDto>>(
                updatedCampaign => Ok(updatedCampaign),
                notFound => NotFound(),
                error => BadRequest(error.Value)
            );
        }

        [HttpDelete("{id:guid}")]
        [RequirePermission(PermissionType.CampaignWrite, "id")]
        public async Task<IActionResult> DeleteCampaign(Guid workspaceId, Guid id)
        {
            var result = await _mediator.Send(new DeleteCampaignCommand(id));

            return result.Match<IActionResult>(
                success => NoContent(),
                notFound => NotFound()
            );
        }
    }
}