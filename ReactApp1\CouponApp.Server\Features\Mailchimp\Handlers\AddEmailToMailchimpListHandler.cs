﻿using CouponApp.Server.Services.Integrations;
using Mediator;
using OneOf;
using OneOf.Types;
using System.Text.Json;
using CouponApp.Server.Services;

namespace CouponApp.Server.Features.Mailchimp.Handlers;

public class AddEmailToMailchimpListHandler : IRequestHandler<AddEmailToMailchimpListCommand, OneOf<Success, Error<string>>>
{
    private readonly INangoProxyClient _nangoProxyClient;

    public AddEmailToMailchimpListHandler(INangoProxyServiceFactory nangoProxyServiceFactory)
    {
        _nangoProxyClient = nangoProxyServiceFactory.Create(IntegrationType.Mailchimp);
    }

    public async ValueTask<OneOf<Success, Error<string>>> Handle(AddEmailToMailchimpListCommand request, CancellationToken cancellationToken)
    {
        var endpoint = $"lists/{request.ListId}/members";
        var payload = new
        {
            email_address = request.EmailAddress,
            status = "pending"
        };

        var result = await _nangoProxyClient.PostAsync<JsonElement>(endpoint, request.ConnectionId, payload);

        return result.Match<OneOf<Success, Error<string>>>(
            success => new Success(),
            error =>
            {
                if (error.Value.ToLower().Contains("member exists"))
                {
                    return new Success();
                }
                return error;
            }
        );
    }
}