﻿using CouponApp.Server.Models.DTOs.Mailchimp;
using Mediator;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Mailchimp;

public record GetMailchimpListsQuery(string ConnectionId) : IRequest<OneOf<MailchimpListsResponseDto, Error<string>>>;

public record SearchMailchimpMemberQuery(string ConnectionId, string ListId, string EmailAddress) : IRequest<OneOf<MailchimpSearchMemberResponseDto, Error<string>>>;