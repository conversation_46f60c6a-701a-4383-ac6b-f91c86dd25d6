﻿using System.Text.Json.Serialization;

namespace CouponApp.Server.Models.DTOs.Hubspot;

public class HubspotPropertiesResponseDto
{
    public List<HubspotPropertyDto> Results { get; set; }
}

public class HubspotPropertyDto
{
    public string Name { get; set; }
    public string Label { get; set; }
    public string Type { get; set; }
    public string FieldType { get; set; }
    public string Description { get; set; }
    public string GroupName { get; set; }
    public bool Calculated { get; set; }
    public bool HubspotDefined { get; set; }
    public bool FormField { get; set; }
    public int DisplayOrder { get; set; }   

    public List<HubspotPropertyOptionDto> Options { get; set; }
    
}

public class HubspotPropertyOptionDto
{
    public string Label { get; set; }
    public string Value { get; set; }
}