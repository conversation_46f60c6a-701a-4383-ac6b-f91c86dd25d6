﻿using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;

namespace CouponApp.Server.Mappers;

public static class CampaignMapper
{
    public static CampaignEditorDto ToEditorDto(this Campaign campaign) => new()
    {
        Id = campaign.Id,
        Config = campaign.DraftConfig ?? campaign.Config,
        CreatedByUserId = campaign.CreatedByUserId,
        IsPublished = campaign.IsPublished,
        CreatedAt = campaign.CreatedAt,
        UpdatedAt = campaign.UpdatedAt,
        Name = campaign.Name,
        HasNoCouponsLeft = campaign.HasNoCouponsLeft,
        CampaignDomainSettings = campaign.CampaignDomainSettings?.ToDto(),
        WorkspaceId = campaign.WorkspaceId.ToString(),
        
    };   

    public static CampaignMiniDto ToMiniDto(this Campaign campaign) => new()
    {
        Id = campaign.Id,
        CreatedAt = campaign.CreatedAt,
        UpdatedAt = campaign.UpdatedAt,
        IsPublished = campaign.IsPublished,
        Name = campaign.Name,
        HasNoCouponsLeft = campaign.HasNoCouponsLeft,
        WorkspaceId = campaign.WorkspaceId.ToString()
    };
}