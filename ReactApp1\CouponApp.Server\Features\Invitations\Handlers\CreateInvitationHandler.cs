﻿// File: ~/CouponApp.Server/Features/Invitations/Handlers/CreateInvitationHandler.cs

using CouponApp.Server.Data;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Services;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Invitations.Handlers;

public class CreateInvitationHandler : IRequestHandler<CreateInvitationCommand, OneOf<InvitationDto, NotFound, AlreadyInvited, Error<string>>>
{
    private readonly ApplicationDbContext _context;
    private readonly IEmailService _emailService;

    public CreateInvitationHandler(ApplicationDbContext context, IEmailService emailService)
    {
        _context = context;
        _emailService = emailService;
    }

    public async ValueTask<OneOf<InvitationDto, NotFound, AlreadyInvited, Error<string>>> Handle(CreateInvitationCommand request, CancellationToken cancellationToken)
    {
        var organization = await _context.Organizations
            .Include(o => o.Members)
            .ThenInclude(m => m.UserProfile)
            .FirstOrDefaultAsync(o => o.Id == request.OrganizationId, cancellationToken);

        if (organization == null)
        {
            return new NotFound();
        }

        if (organization.Members.Any(m => m.UserProfile.Email == request.Email))
        {
            return new AlreadyInvited("User is already a member of the organization");
        }

        var existingInvitation = await _context.Invitations.FirstOrDefaultAsync(i => i.OrganizationId == request.OrganizationId && i.Email == request.Email, cancellationToken);
        if (existingInvitation != null)
        {
            return new AlreadyInvited("User has already been invited");
        }

        var invitation = new Invitation
        {
            Email = request.Email,
            OrganizationId = request.OrganizationId,
            InvitationToken = Guid.NewGuid().ToString(),
            InvitedByUserId = request.InvitedByUserId,
            ExpiresAt = DateTime.UtcNow.AddDays(7)
        };

        _context.Invitations.Add(invitation);

        var invitationLink = $"https://dev.couponapp.beakbyte.com/invitation/{invitation.InvitationToken}";
        var emailResult = await _emailService.SendInvitationEmailAsync(request.Email, invitationLink);

        if (emailResult.IsT1)
        {
            return new Error<string>($"Error sending invitation email: {emailResult.AsT1.Value}");
        }

        await _context.SaveChangesAsync(cancellationToken);

        return new InvitationDto
        {
            Id = invitation.Id,
            Email = invitation.Email,
            ExpiresAt = invitation.ExpiresAt
        };
    }
}

