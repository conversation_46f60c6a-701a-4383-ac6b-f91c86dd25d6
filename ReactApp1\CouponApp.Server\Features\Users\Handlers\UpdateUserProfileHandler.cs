﻿using CouponApp.Server.Data;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;
using Slugify;

namespace CouponApp.Server.Features.Users.Handlers;

public class UpdateUserProfileHandler : IRequestHandler<UpdateUserProfileCommand, OneOf<bool, Error<string>>>
{
    private readonly ApplicationDbContext _context;
    private readonly SlugHelper _slugHelper;
    private readonly IMediator _mediator;
    
    public UpdateUserProfileHandler(ApplicationDbContext context, IMediator mediator)
    {
        _context = context;
        _mediator = mediator;
        _slugHelper = new SlugHelper();
    }

    public async ValueTask<OneOf<bool, Error<string>>> Handle(UpdateUserProfileCommand request,
        CancellationToken cancellationToken)
    {

        var user = await _context.UserProfiles
            // .Include(u => u.Organizations)
            .FirstOrDefaultAsync(u => u.Id == request.UserId);

       
        
        if (user == null)
        {
           return new Error<string>("User not found");
        }

        user.FullName = request.Name ?? "";
        user.Email = request.PrimaryEmail;
       await _context.SaveChangesAsync();
           
        
        return true;
    }
}