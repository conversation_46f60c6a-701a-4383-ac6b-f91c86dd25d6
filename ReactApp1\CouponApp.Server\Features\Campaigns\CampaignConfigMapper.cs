﻿using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;

namespace CouponApp.Server.Mappers;

public static class CampaignConfigMapper
{
    public static CampaignConfigDto ToPublicConfigDto(this CampaignConfigDto configDto) => new()
    {   
        Flows = configDto.Flows?.Select(flow => new CampaignFlow
        {
            Id = flow.Id,
            Name = flow.Name,
            Nodes = flow.Nodes
                .Select(n => new CampaignFlowNode
                {
                    Id = n.Id,
                    Type = n.Type,
                    Position = null, // Remove position
                    Connections = n.Connections,
                    ElseConnection = n.ElseConnection,
                    // Remove payload for server nodes
                    Payload = n.Type.StartsWith("server") ? null : n.Payload
                })
                .ToArray()
        }).ToArray(),
        
        Scenes = configDto.Scenes,
        Variables = configDto.Variables,
        Revision = configDto.Revision,
        UsedAssetIds = configDto.UsedAssetIds
    };
    
    
}