﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CouponApp.Server.Services.Integrations;

namespace CouponApp.Server.Models.Entities;

public class OrganizationIntegration : BaseEntity
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public string Id { get; set; }
    
    [Required]
    public Guid OrganizationId { get; set; }

    [ForeignKey("OrganizationId")]
    public Organization Organization { get; set; }

    [Required]
    public IntegrationType IntegrationType { get; set; }

    public string? ConnectionId { get; set; }
    
    public string? Name { get; set; }

      [Column(TypeName = "jsonb")]
     public Dictionary<string, object>? AdditionalData { get; set; } = new Dictionary<string, object>();
}

