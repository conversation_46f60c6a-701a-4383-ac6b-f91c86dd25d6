﻿using System.Text.Json;
using CouponApp.Server.Features.Hubspot;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Services.NodeExecution.Interfaces;
using Mediator;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Services.Executors;

public class SendFormToHubspotNodeExecutor : INodeExecutor
{
    private readonly ILogger<SendFormToHubspotNodeExecutor> _logger;
    private readonly IVariablesStringInterpolationService _variablesStringInterpolationService;
    private readonly IMediator _mediator;

    public SendFormToHubspotNodeExecutor(
        ILogger<SendFormToHubspotNodeExecutor> logger,
        IVariablesStringInterpolationService variablesStringInterpolationService,
        IMediator mediator)
    {
        _logger = logger;
        _variablesStringInterpolationService = variablesStringInterpolationService;
        _mediator = mediator;
    }

    public async Task<OneOf<Success, Error<string>>> ExecuteAsync(Guid campaignId, CampaignFlowNode node, Dictionary<string, object> variables)
    {
        try
        {
            var mappings = new Dictionary<string, object>();
            if (node.Payload.TryGetValue("mappings", out var mappingsValue))
            {
                if (mappingsValue is JsonElement jsonElement)
                {
                    mappings = jsonElement.Deserialize<Dictionary<string, object>>() ?? new Dictionary<string, object>();
                }
                else if (mappingsValue is Dictionary<string, object> dict)
                {
                    mappings = dict;
                }
            }

            // Interpolate variables in the mappings
            var interpolatedMappings = new Dictionary<string, object>();
          
            foreach (var kvp in mappings)
            {
                var interpolatedValue = _variablesStringInterpolationService.InterpolateVariables(kvp.Value?.ToString() ?? string.Empty, variables);
                interpolatedMappings[kvp.Key] = interpolatedValue;
            }

            var result = await _mediator.Send(new AddHubspotContactCommand(campaignId, interpolatedMappings));

            return result.Match<OneOf<Success, Error<string>>>(
                success => new Success(),
                error => new Error<string>($"Failed to add contact to HubSpot: {error.Value}")
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing SendFormToHubspotNode node {NodeId}", node.Id);
            return new Error<string>($"Error executing SendFormToHubspotNode: {ex.Message}");
        }
    }
}