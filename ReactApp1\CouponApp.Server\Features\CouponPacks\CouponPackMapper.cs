﻿using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;

namespace CouponApp.Server.Mappers;

public static class CouponPackMapper
{
    public static CouponPackDto ToDto(this CouponPack couponPack) => new()
    {
        Id = couponPack.Id,
        Name = couponPack.Name,
        CampaignId = couponPack.CampaignId,
        Coupons = couponPack.Coupons?.Select(c => c.ToDto()).ToList() ?? new List<CouponDto>(),
        CreatedAt = couponPack.CreatedAt,
        UpdatedAt = couponPack.UpdatedAt,
        DeleteCouponOnRedeem = couponPack.DeleteCouponOnRedeem,
        ShouldCheckingForUserParticipation = couponPack.ShouldCheckingForUserParticipation,
        NotifyThreshold = couponPack.NotifyThreshold,
        InterruptThreshold = couponPack.InterruptThreshold
    };

    public static CouponPackMiniDto ToMiniDto(this CouponPack couponPack) => new()
    {
        Id = couponPack.Id,
        Name = couponPack.Name,
        CampaignId = couponPack.CampaignId,
        CouponsCount = couponPack.Coupons?.Count ?? -1,
        CreatedAt = couponPack.CreatedAt,
        UpdatedAt = couponPack.UpdatedAt,
        DeleteCouponOnRedeem = couponPack.DeleteCouponOnRedeem,
        ShouldCheckingForUserParticipation = couponPack.ShouldCheckingForUserParticipation,
        NotifyThreshold = couponPack.NotifyThreshold,
        InterruptThreshold = couponPack.InterruptThreshold
    };
    public static CouponDto ToDto(this Coupon coupon) => new()
    {
        Id = coupon.Id,
        Value = coupon.Value
    };
}