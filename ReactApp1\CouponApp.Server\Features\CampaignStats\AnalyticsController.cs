﻿using Microsoft.AspNetCore.Mvc;
using CouponApp.Server.Features.CampaignStats;
using CouponApp.Server.Models.DTOs;
using Mediator;
using Microsoft.AspNetCore.Authorization;

namespace CouponApp.Server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class AnalyticsController : ControllerBase
    {
        private readonly IMediator _mediator;

        public AnalyticsController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpGet("campaign/{campaignId}")]
        public async Task<ActionResult<CampaignAnalyticsDto>> GetCampaignAnalytics(Guid campaignId, string dateRange)
        {
            var result = await _mediator.Send(new GetCampaignAnalyticsQuery(campaignId, dateRange));

            return result.Match<ActionResult<CampaignAnalyticsDto>>(
                analytics => Ok(analytics),
                notFound => NotFound("Campaign not found or analytics data not available.")
            );
        }

    }
}