﻿using CouponApp.Server.Models;
using CouponApp.Server.Services.Executors;
using CouponApp.Server.Services.NodeExecution.Interfaces;

namespace CouponApp.Server.Services.NodeExecution;

public class NodeExecutorFactory : INodeExecutorFactory
{
    private readonly IServiceProvider _serviceProvider;

    public NodeExecutorFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public INodeExecutor CreateExecutor(string nodeType)
    {
        return nodeType switch
        {
            CampaignNodeType.Webhook => _serviceProvider.GetRequiredService<WebhookNodeExecutor>(),
            CampaignNodeType.AddContactToMailchimpList => _serviceProvider.GetRequiredService<MailchimpAddToListNodeExecutor>(),
            CampaignNodeType.SendFormToHubspot => _serviceProvider.GetRequiredService<SendFormToHubspotNodeExecutor>(),
            CampaignNodeType.AddCustomerToShopify => _serviceProvider.GetRequiredService<ShopifyAddCustomerNodeExecutor>(),
            _ => throw new ArgumentException($"Unsupported node type: {nodeType}")
        };
    }
}