﻿using CouponApp.Server.Data;
using CouponApp.Server.Features.Campaigns;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Models.Errors;
using CouponApp.Server.Services;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Campaigns.Handlers;

public class ExecuteNodeHandler : IRequestHandler<ExecuteNodeCommand, OneOf<Success, NotFound, StringError>>
{
    private readonly ApplicationDbContext _context;
    private readonly INodeExecutionService _nodeExecutionService;

    public ExecuteNodeHandler(ApplicationDbContext context, INodeExecutionService nodeExecutionService)
    {
        _context = context;
        _nodeExecutionService = nodeExecutionService;
    }

    public async ValueTask<OneOf<Success, NotFound, StringError>> Handle(ExecuteNodeCommand request, CancellationToken cancellationToken)
    {
        var campaign = await _context.Campaigns
            .FirstOrDefaultAsync(c => c.Id == request.CampaignId, cancellationToken);

        if (campaign == null)
        {
            return new NotFound();
        }

        var nodes = MapConnectedNodes(campaign.Config.Flows.SelectMany(f => f.Nodes).ToList(), request.NodeId);

        if (nodes.Count == 0)
        {
            return new NotFound();
        }

        if (nodes.Any(n => !n.Type.StartsWith("server")))
        {
            return new StringError("Improper node configuration: There is non-server node after a server node.");
        }

        await _nodeExecutionService.ExecuteNodesAsync(request.CampaignId, nodes, request.Variables);

        return new Success();
    }

    private List<CampaignFlowNode> MapConnectedNodes(List<CampaignFlowNode> allNodes, string startNodeId)
    {
        var nodesToExecute = new List<CampaignFlowNode>();
        var visitedNodeIds = new HashSet<string>();
        var nodeQueue = new Queue<string>();

        nodeQueue.Enqueue(startNodeId);

        while (nodeQueue.Count > 0)
        {
            var currentNodeId = nodeQueue.Dequeue();

            if (visitedNodeIds.Contains(currentNodeId))
            {
                continue;
            }

            var currentNode = allNodes.FirstOrDefault(n => n.Id == currentNodeId);

            if (currentNode != null)
            {
                nodesToExecute.Add(currentNode);
                visitedNodeIds.Add(currentNodeId);

                foreach (var connectionId in currentNode.Connections)
                {
                    if (!visitedNodeIds.Contains(connectionId))
                    {
                        nodeQueue.Enqueue(connectionId);
                    }
                }
            }
        }

        return nodesToExecute;
    }
}