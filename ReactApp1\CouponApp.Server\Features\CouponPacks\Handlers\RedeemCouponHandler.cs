﻿using CouponApp.Server.Data;
using CouponApp.Server.Mappers;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Errors;
using CouponApp.Server.Services;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.CouponPacks.Handlers;

public class RedeemCouponHandler : IRequestHandler<RedeemCouponCommand, OneOf<CouponDto, NotFound, UserAlreadyParticipated>>
{
    private readonly ApplicationDbContext _context;
    private readonly ICampaignSessionService _campaignSessionService;
    private readonly IMediator _mediator;

    public RedeemCouponHandler(
        ApplicationDbContext context, 
        ICampaignSessionService campaignSessionService,
        IMediator mediator)
    {
        _context = context;
        _campaignSessionService = campaignSessionService;
        _mediator = mediator;
    }

    public async ValueTask<OneOf<CouponDto, NotFound, UserAlreadyParticipated>> Handle(RedeemCouponCommand request, CancellationToken cancellationToken)
    {
        await using var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);

        try
        {
            var couponPack = await _context.CouponPacks
                .Include(cp => cp.Campaign)
                .FirstOrDefaultAsync(cp => cp.Id == request.Id, cancellationToken);

            if (couponPack == null)
            {
                return new NotFound();
            }

            if (couponPack.ShouldCheckingForUserParticipation)
            {
                var hasParticipated = await _campaignSessionService.HasParticipatedInCampaignAsync(couponPack.CampaignId, request.Fingerprint);
                if (hasParticipated)
                {
                    return new UserAlreadyParticipated();
                }
            }

            var coupon = await _context.Coupons
                .Where(c => c.CouponPackId == request.Id)
                .OrderBy(c => EF.Functions.Random())
                .FirstOrDefaultAsync(cancellationToken);

            if (coupon == null)
            {
                return new NotFound();
            }

            await _campaignSessionService.GetOrCreateSessionAsync(couponPack.CampaignId, request.Fingerprint);

            var couponDto = coupon.ToDto();

            if (couponPack.DeleteCouponOnRedeem)
            {
                _context.Coupons.Remove(coupon);
            }

            await _context.SaveChangesAsync(cancellationToken);
            await transaction.CommitAsync(cancellationToken);

            // Use mediator to publish a notification instead of directly calling a service
            await _mediator.Publish(new CouponPackChangedNotification(couponPack.Id), cancellationToken);

            return couponDto;
        }
        catch (Exception)
        {
            await transaction.RollbackAsync(cancellationToken);
            throw;
        }
    }
}