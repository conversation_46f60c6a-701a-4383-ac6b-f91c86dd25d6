﻿namespace CouponApp.Server.Features.CustomDomains;

using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.DTOs.Requests;
using CouponApp.Server.Models.Enums;
using Mediator;
using OneOf;
using OneOf.Types;


    // Features/CustomDomains/Queries.cs
    public record GetCustomDomainsQuery(Guid OrganizationId) 
        : IRequest<List<CustomDomainDto>>;

    public record GetCustomDomainQuery(Guid OrganizationId, Guid DomainId) 
        : IRequest<OneOf<CustomDomainDto, NotFound>>;


