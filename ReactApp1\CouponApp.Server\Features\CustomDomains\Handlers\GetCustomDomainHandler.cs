﻿namespace CouponApp.Server.Features.CustomDomains.Handlers;

using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Data;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

public class GetCustomDomainHandler : IR<PERSON><PERSON><PERSON>andler<GetCustomDomainQuery, OneOf<CustomDomainDto, NotFound>>
{
    private readonly ApplicationDbContext _context;

    public GetCustomDomainHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<CustomDomainDto, NotFound>> Handle(
        GetCustomDomainQuery request,
        CancellationToken cancellationToken)
    {
        var domain = await _context.CustomDomains
            .FirstOrDefaultAsync(d =>
                    d.Id == request.DomainId &&
                    d.OrganizationId == request.OrganizationId,
                cancellationToken);

        if (domain == null)
        {
            return new NotFound();
        }

        return new CustomDomainDto
        {
            Id = domain.Id,
            DomainName = domain.DomainName,
            Status = domain.Status,
            ErrorMessage = domain.ErrorMessage,
            HasValidSsl = domain.HasValidSsl,
          
        };
    }
}