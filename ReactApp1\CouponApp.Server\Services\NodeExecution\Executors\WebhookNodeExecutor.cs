﻿using CouponApp.Server.Models.Entities;
using CouponApp.Server.Services.NodeExecution.Interfaces;
using Microsoft.Extensions.Caching.Distributed;
using OneOf.Types;
using Polly;
using Polly.Retry;
using OneOf;

namespace CouponApp.Server.Services.Executors;

public class WebhookNodeExecutor : INodeExecutor
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger<WebhookNodeExecutor> _logger;
    private readonly AsyncRetryPolicy _retryPolicy;
    private readonly IVariablesStringInterpolationService _variablesStringInterpolationService;

    public WebhookNodeExecutor(IHttpClientFactory httpClientFactory, ILogger<WebhookNodeExecutor> logger, IVariablesStringInterpolationService variablesStringInterpolationService)
    {
        _httpClientFactory = httpClientFactory;
        _logger = logger;
        _variablesStringInterpolationService = variablesStringInterpolationService;
        _retryPolicy = Policy
            .Handle<HttpRequestException>()
            .WaitAndRetryAsync(3, retryAttempt => 
                TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));
    }

    public async Task<OneOf<Success, Error<string>>> ExecuteAsync(Guid campaignId, CampaignFlowNode node, Dictionary<string, object> variables)
    {
        try
        {
            var result = await _retryPolicy.ExecuteAsync(async () =>
            {
                node.Payload.TryGetValue("url", out var url);
                node.Payload.TryGetValue("method", out var method);
                node.Payload.TryGetValue("payload", out var newPayload);
                
                var interpolatedUrl = _variablesStringInterpolationService.InterpolateVariables(url?.ToString() ?? string.Empty, variables);
                var interpolatedPayload = _variablesStringInterpolationService.InterpolateVariables(newPayload?.ToString() ?? string.Empty, variables);
                
                var client = _httpClientFactory.CreateClient();
                var response =  method?.ToString() switch
                {
                    "GET" => await client.GetAsync(interpolatedUrl),
                    "POST" => await client.PostAsJsonAsync(interpolatedUrl, interpolatedPayload),
                    "PUT" => await client.PutAsJsonAsync(interpolatedUrl, interpolatedPayload),
                    "DELETE" => await client.DeleteAsync(interpolatedUrl),
                    _ => await client.PostAsJsonAsync(interpolatedUrl, interpolatedPayload),
                };
                 
                response.EnsureSuccessStatusCode();
                return response;
            });

            return new Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing webhook node {NodeId}", node.Id);
            return new Error<string>($"Error executing webhook: {ex.Message}");
        }
    }
}