﻿// File: ~/CouponApp.Server/Features/Organizations/Queries.cs

using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Errors;
using CouponApp.Server.Services.Integrations;
using Mediator;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Organizations;

public record GetOrganizationForUserByShortIdQuery(string UserId, string ShortId) : IRequest<OneOf<OrganizationDto, NotFound>>;
public record GetOrganizationMembersQuery(Guid OrganizationId) : IRequest<OneOf<OrganizationMembersResponse, NotFound>>;
public record GetIntegrationsQuery(Guid OrganizationId) : IRequest<OneOf<List<OrganizationIntegrationDto>, NotFound>>;
public record GetMailchimpListsQuery(Guid OrganizationId) : IRequest<OneOf<object, NotFound, Error<string>>>;
public record CheckOrganizationMembershipQuery(Guid OrganizationId, string UserId) : IRequest<bool>;

public record GetIntegrationByIdQuery(Guid OrganizationId, IntegrationType IntegrationType) : IRequest<OneOf<OrganizationIntegrationDto, NotFound>>;

public record DeleteOrganizationMemberCommand(Guid OrganizationId, string UserId) : IRequest<OneOf<Success, NotFound, UnauthorizedError>>;