﻿using CouponApp.Server.Features.LeadForms;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;
using Mediator;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CouponApp.Server.Controllers;

[ApiController]
[Route("api/lead-forms")]
public class LeadFormsController : ControllerBase
{
    private readonly IMediator _mediator;

    public LeadFormsController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpPost]
    [AllowAnonymous]
    public async Task<IActionResult> SubmitLeadForm([FromBody] LeadFormSubmissionDto submissionDto)
    {
        var result = await _mediator.Send(new SubmitLeadFormCommand(submissionDto));

        return result.Match<IActionResult>(
            leadForm => Ok(leadForm.Id),
            error => BadRequest(error.Value)
        );
    }

    [HttpGet("campaign/{campaignId}")]
    [Authorize]
    public async Task<IActionResult> GetLeadFormsByCampaign(Guid campaignId)
    {
        var result = await _mediator.Send(new GetLeadFormsByCampaignQuery(campaignId));
        return Ok(result);
    }
}