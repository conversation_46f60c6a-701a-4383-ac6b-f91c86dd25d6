﻿using CouponApp.Server.Data;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CouponApp.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class HealthController : ControllerBase
{
    
    public static string test = Guid.NewGuid().ToString();
    private readonly ApplicationDbContext _context;

    public HealthController(ApplicationDbContext context)
    {
        _context = context;
    }

    [HttpGet]
    [ResponseCache(NoStore = true, Location = ResponseCacheLocation.None)]
    public IActionResult Get()
    {
        return Ok("Healthy " + test + " | " + DateTime.UtcNow.ToLongTimeString());
    }
    
    [HttpGet("db")]
    public async Task<IActionResult> GetDbHealth()
    {
        var testCampaign = await _context.Campaigns.FirstOrDefaultAsync();
        return Ok("Healthy " + testCampaign.Name);
    }
}