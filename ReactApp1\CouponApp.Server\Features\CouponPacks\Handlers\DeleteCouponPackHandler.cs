﻿using CouponApp.Server.Data;
using Mediator;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.CouponPacks.Handlers;

public class DeleteCouponPackHandler : IRequestHandler<DeleteCouponPackCommand, OneOf<Success, NotFound>>
{
    private readonly ApplicationDbContext _context;

    public DeleteCouponPackHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<Success, NotFound>> <PERSON>le(DeleteCouponPackCommand request, CancellationToken cancellationToken)
    {
        var couponPack = await _context.CouponPacks.FindAsync(new object[] { request.Id }, cancellationToken);
        if (couponPack == null)
        {
            return new NotFound();
        }

        _context.CouponPacks.Remove(couponPack);
        await _context.SaveChangesAsync(cancellationToken);

        return new Success();
    }
}