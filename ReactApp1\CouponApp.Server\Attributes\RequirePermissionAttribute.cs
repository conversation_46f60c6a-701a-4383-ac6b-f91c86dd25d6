﻿using CouponApp.Server.Extensions;
using CouponApp.Server.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.OpenApi.Extensions;

namespace CouponApp.Server.Attributes;

[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, Inherited = true, AllowMultiple = true)]
public class RequirePermissionAttribute : Attribute, IAsyncAuthorizationFilter
{
    private readonly PermissionType _permissionType;
    private readonly string _idParameterName;

    public RequirePermissionAttribute(PermissionType permissionType, string idParameterName)
    {
        _permissionType = permissionType;
        _idParameterName = idParameterName;
    }

    public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
    {
        var permissionService = context.HttpContext.RequestServices.GetRequiredService<PermissionService>();
        var userId = context.HttpContext.User.GetId();

        if (!context.ActionDescriptor.Parameters.Any(p => p.Name == _idParameterName))
        {
            context.Result = new BadRequestObjectResult($"Missing required parameter: {_idParameterName}");
            return;
        }

        var id = context.HttpContext.Request.RouteValues[_idParameterName]?.ToString();
        if (id == null )
        {
            context.Result = new BadRequestObjectResult($"Invalid {_idParameterName} parameter");
            return;
        }

        var hasPermission = _permissionType switch
        {
            PermissionType.CampaignWrite => await permissionService.ForUser(userId).CanWriteCampaign(id),
            PermissionType.OrganizationRead => await permissionService.ForUser(userId).CanReadOrganization(id),
            PermissionType.OrganizationWrite => await permissionService.ForUser(userId).CanWriteOrganization(id),
            PermissionType.OrganizationMemberDelete => await permissionService.ForUser(userId).CanDeleteOrganizationMember(id),
            _ => false
        };

        if (!hasPermission)
        {
            context.Result = new UnauthorizedObjectResult($"You don't have this permission: {_permissionType.GetDisplayName()}");
        }
    }
}

public enum PermissionType
{
    CampaignWrite,
    OrganizationRead,
    OrganizationWrite,
    OrganizationMemberDelete,
}