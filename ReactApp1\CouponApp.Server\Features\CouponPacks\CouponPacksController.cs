﻿using CouponApp.Server.Extensions;
using CouponApp.Server.Features.CouponPacks;
using CouponApp.Server.Models.DTOs;
using Mediator;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CouponApp.Server.Controllers;

[ApiController]
[Route("api/coupon-packs")]
[Authorize]
public class CouponPacksController : ControllerBase
{
    private readonly IMediator _mediator;

    public CouponPacksController(IMediator mediator)
    {
        _mediator = mediator;
    }
    
    [HttpPut("{id:guid}")]
    public async Task<ActionResult<CouponPackDto>> UpdateCouponPack(Guid id, [FromBody] UpdateCouponPackDto updateDto)
    {
        var command = new UpdateCouponPackCommand(id, updateDto);
        var result = await _mediator.Send(command);

        return result.Match<ActionResult<CouponPackDto>>(
            couponPack => Ok(couponPack),
            notFound => NotFound()
        );
    }

    [HttpGet("campaign/{campaignId:guid}")]
    public async Task<ActionResult<IEnumerable<CouponPackDto>>> GetCampaignCouponPacks(Guid campaignId)
    {
        var query = new GetCampaignCouponPacksQuery(campaignId);
        var result = await _mediator.Send(query);
        return Ok(result);
    }
    
  

    [HttpPost]
    public async Task<ActionResult<CouponPackDto>> CreateCouponPack([FromBody] CreateCouponPackDto createDto)
    {
        var command = new CreateCouponPackCommand(createDto);
        var result = await _mediator.Send(command);
        return CreatedAtAction(nameof(GetCouponPack), new { id = result.Id }, result);
    }

    [HttpGet("{id:guid}")]
    public async Task<ActionResult<CouponPackDto>> GetCouponPack(Guid id)
    {
        var query = new GetCouponPackQuery(id);
        var result = await _mediator.Send(query);

        return result.Match<ActionResult<CouponPackDto>>(
            couponPack => Ok(couponPack),
            notFound => NotFound()
        );
    }
    
    [HttpGet("{id:guid}/coupons/count")]
    [AllowAnonymous]
    public async Task<ActionResult<IEnumerable<CouponPackCountDto>>> GetCouponPackCouponsLeftCount(Guid id)
    {
        var query = new GetCouponPackCouponsLeftCountQuery(id);
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    [HttpPost("{id:guid}/coupons")]
    public async Task<ActionResult<CouponPackDto>> AddCouponsToPack(Guid id, [FromBody] AddCouponsDto addCouponsDto)
    {
        var command = new AddCouponsToCouponPackCommand(id, addCouponsDto.CouponValues);
        var result = await _mediator.Send(command);

        return result.Match<ActionResult<CouponPackDto>>(
            couponPack => Ok(couponPack),
            notFound => NotFound(),
            fullError => BadRequest("You can only have up to 1000 coupons in this pack")
        );
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> DeleteCouponPack(Guid id)
    {
        var command = new DeleteCouponPackCommand(id);
        var result = await _mediator.Send(command);

        return result.Match<IActionResult>(
            success => NoContent(),
            notFound => NotFound()
        );
    }

    [HttpGet("{id:guid}/redeem-coupon")]
    [AllowAnonymous]
    public async Task<ActionResult<CouponDto>> RedeemCoupon(Guid id)
    {
        var fingerprint = HttpContext.GetFingerprint();
        var command = new RedeemCouponCommand(id, fingerprint);
        var result = await _mediator.Send(command);

        return result.Match<ActionResult<CouponDto>>(
            coupon => Ok(coupon),
            notFound => NotFound(),
            userAlreadyParticipated => ValidationProblem(userAlreadyParticipated.Message)
        );
    }
}