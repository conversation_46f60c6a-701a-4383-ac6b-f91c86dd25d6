﻿using CouponApp.Server.Data;
using CouponApp.Server.Mappers;
using CouponApp.Server.Models.DTOs;
using Mediator;
using Microsoft.EntityFrameworkCore;

namespace CouponApp.Server.Features.CouponPacks.Handlers;

public class GetCampaignCouponPacksHandler : IRequestHandler<GetCampaignCouponPacksQuery, IEnumerable<CouponPackMiniDto>>
{
    private readonly ApplicationDbContext _context;

    public GetCampaignCouponPacksHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<IEnumerable<CouponPackMiniDto>> Handle(GetCampaignCouponPacksQuery request, CancellationToken cancellationToken)
    {
        return await _context.CouponPacks
            .Include(cp => cp.Coupons)
            .Where(cp => cp.CampaignId == request.CampaignId)
            .OrderByDescending(cp => cp.CreatedAt)
            .Select(cp => cp.ToMiniDto())
            .ToListAsync(cancellationToken);
    }
}