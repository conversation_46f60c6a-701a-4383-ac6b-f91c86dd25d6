﻿using CouponApp.Server.Features.Campaigns;
using CouponApp.Server.Features.Organizations;
using CouponApp.Server.Models.DTOs.Shopify;
using CouponApp.Server.Services.Integrations;
using Mediator;
using OneOf;
using OneOf.Types;
using System.Text.Json;
using CouponApp.Server.Data;
using CouponApp.Server.Services;
using Microsoft.EntityFrameworkCore;

namespace CouponApp.Server.Features.Shopify.Handlers;

public class AddShopifyCustomerHandler : IRequestHandler<AddShopifyCustomerCommand, OneOf<ShopifyCustomerDto, Error<string>>>
{
    private readonly IMediator _mediator;
    private readonly INangoProxyClient _nangoProxyClient;
    private readonly ILogger<AddShopifyCustomerHandler> _logger;
    private readonly ApplicationDbContext _context;
    public AddShopifyCustomerHandler(
        IMediator mediator,
        INangoProxyServiceFactory nangoProxyServiceFactory,
        ILogger<AddShopifyCustomerHandler> logger, ApplicationDbContext context)
    {
        _mediator = mediator;
        _nangoProxyClient = nangoProxyServiceFactory.Create(IntegrationType.Shopify);
        _logger = logger;
        _context = context;
    }

    public async ValueTask<OneOf<ShopifyCustomerDto, Error<string>>> Handle(AddShopifyCustomerCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var campaign = await _context.Campaigns.FirstOrDefaultAsync(c => c.Id == request.CampaignId, cancellationToken);
            if (campaign == null)
            {
                return new Error<string>("Campaign not found");
            }

            var integrationResult = await _mediator.Send(new GetIntegrationByIdQuery(campaign.WorkspaceId, IntegrationType.Shopify));
            if (!integrationResult.TryPickT0(out var integration, out var _))
            {
                return new Error<string>("Shopify integration not found. Please create a Shopify integration first.");
            }

            var customerData = new ShopifyCustomerCreateRequest
            {
                Customer = new ShopifyCustomerDto
                {
                    Email = request.Email,
                    Phone = request.Phone,
                    FirstName = request.FirstName,
                    LastName = request.LastName,
                    Tags = request.Tag
                }
            };

            var endpoint = "admin/api/2024-07/customers.json";
            var result = await _nangoProxyClient.PostAsync<ShopifyCustomerCreateResponse>(endpoint, integration.ConnectionId, customerData);

            return result.Match<OneOf<ShopifyCustomerDto, Error<string>>>(
                success => success.Customer,
                error => new Error<string>($"Failed to add customer to Shopify: {error.Value}")
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding customer to Shopify");
            return new Error<string>($"Error adding customer to Shopify: {ex.Message}");
        }
    }
}