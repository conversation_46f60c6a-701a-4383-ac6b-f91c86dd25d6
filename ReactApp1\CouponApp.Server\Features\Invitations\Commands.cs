﻿using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Services;
using Mediator;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Invitations;


public record CreateInvitationCommand(Guid OrganizationId, string Email, string InvitedByUserId) : IRequest<OneOf<InvitationDto, NotFound, AlreadyInvited, Error<string>>>;

public record AcceptInvitationCommand(string Token, string UserId) : IRequest<OneOf<Success, NotFound, InvalidInvitation>>;
public record DeleteInvitationCommand(Guid Id) : IRequest<OneOf<Success, NotFound, InvalidInvitation>>;
