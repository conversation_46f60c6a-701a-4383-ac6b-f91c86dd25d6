﻿using Mediator;

namespace CouponApp.Server.Features.CouponPacks;

public record CouponPackChangedNotification(Guid CouponPackId) : INotification;

public record CampaignHasNoCouponsLeftNotification(Guid CampaignId) : INotification;

public record CouponPackReachedNotificationTreshold(string Email, string CampaignName, string CouponPackName, int RemainingCoupons, string CouponPackId, string CampaignId) : INotification;