﻿using System.Text.RegularExpressions;

namespace CouponApp.Server.Services;

public interface IVariablesStringInterpolationService
{
    string InterpolateVariables(string input, IDictionary<string, object> variables);
}

public class VariablesStringInterpolationService : IVariablesStringInterpolationService
{
    private static readonly Regex VariablePattern = new Regex(@"\{\{(.*?)\}\}", RegexOptions.Compiled);

    public string InterpolateVariables(string input, IDictionary<string, object> variables)
    {
        if (string.IsNullOrEmpty(input))
        {
            return input;
        }

        return VariablePattern.Replace(input, match =>
        {
            string key = match.Groups[1].Value.Trim();
            if (variables.TryGetValue(key, out var value))
            {
                return value?.ToString() ?? string.Empty;
            }

            return match.Value; // Keep the original placeholder if the variable is not found
        });
    }
}