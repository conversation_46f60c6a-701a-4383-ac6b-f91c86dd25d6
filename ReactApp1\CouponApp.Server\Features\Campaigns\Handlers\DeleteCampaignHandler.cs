﻿using CouponApp.Server.Data;
using Mediator;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Campaigns.Handlers;

public class DeleteCampaignHandler : IRequestHandler<DeleteCampaignCommand, OneOf<Success, NotFound>>
{
    private readonly ApplicationDbContext _context;

    public DeleteCampaignHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<Success, NotFound>> <PERSON><PERSON>(DeleteCampaignCommand request, CancellationToken cancellationToken)
    {
        var campaign = await _context.Campaigns.FindAsync(new object[] { request.Id }, cancellationToken);
        if (campaign == null)
        {
            return new NotFound();
        }

        _context.Campaigns.Remove(campaign);
        await _context.SaveChangesAsync(cancellationToken);

        return new Success();
    }
}