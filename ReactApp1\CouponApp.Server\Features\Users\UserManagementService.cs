﻿using System.Net.Http.Json;
using System.Text.Json;
using System.Text.Json.Serialization;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Services
{
    public interface IUserManagementService
    {
        Task<OneOf<Success, Error<string>>> UpdateUserProfileAsync(string userId, UpdateUserProfileDto dto);
        Task<OneOf<Success, Error<string>>> UpdateUserPasswordAsync(string userId, UpdateUserPasswordDto dto);
        Task<OneOf<LogtoUserProfileDto, Error<string>>> GetUserProfileAsync(string userId);
    }

    public class UserManagementService : IUserManagementService
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ILogger<UserManagementService> _logger;

        public UserManagementService(IHttpClientFactory httpClientFactory, ILogger<UserManagementService> logger)
        {
            _httpClientFactory = httpClientFactory;
            _logger = logger;
        }

        public async Task<OneOf<Success, Error<string>>> UpdateUserProfileAsync(string userId, UpdateUserProfileDto dto)
        {
            var client = _httpClientFactory.CreateClient("LogtoClient");
            var requestUrl = $"/api/users/{userId}";

            var payload = new
            {
                name = dto.Name,
                primaryEmail = dto.PrimaryEmail
            };

            var response = await client.PatchAsJsonAsync(requestUrl, payload);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("User profile updated successfully for user {UserId}", userId);
                return new Success();
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            _logger.LogError("Failed to update user profile for user {UserId}. Status Code: {StatusCode}, Error: {Error}", 
                userId, response.StatusCode, errorContent);
            return new Error<string>($"Failed to update user profile. Status Code: {response.StatusCode}, Error: {errorContent}");
        }

        public async Task<OneOf<Success, Error<string>>> UpdateUserPasswordAsync(string userId, UpdateUserPasswordDto dto)
        {
            
            var userProfileResult = await GetUserProfileAsync(userId);
            
            
            if(!userProfileResult.TryPickT0(out var userProfile, out var _))
            {
                return new Error<string>("User profile not found");
            }

            if (userProfile.HasPassword)
            {
                var isCurrentPasswordValid = await VerifyPasswordAsync(userId, dto.CurrentPassword);
                if (!isCurrentPasswordValid.IsT0)
                {
                    return new Error<string>(isCurrentPasswordValid.AsT1.Value);
                }
            }

            
            if(dto.NewPassword != dto.ConfirmPassword)
            {
                return new Error<string>("New password and confirm password do not match");
            }
            
            var client = _httpClientFactory.CreateClient("LogtoClient");
            var requestUrl = $"/api/users/{userId}/password";

            var payload = new
            {
                password = dto.NewPassword
            };

            var response = await client.PatchAsJsonAsync(requestUrl, payload);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("User profile updated successfully for user {UserId}", userId);
                return new Success();
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            _logger.LogError("Failed to update user profile for user {UserId}. Status Code: {StatusCode}, Error: {Error}", 
                userId, response.StatusCode, errorContent);
            return new Error<string>($"Failed to update user profile. Status Code: {response.StatusCode}, Error: {errorContent}");
        }


        public async Task<OneOf<bool, Error<string>>> VerifyPasswordAsync(string userId, string password)
        {
            var client = _httpClientFactory.CreateClient("LogtoClient");
            var requestUrl = $"/api/users/{userId}/password/verify";

            var payload = new { password = password };

            var response = await client.PostAsJsonAsync(requestUrl, payload);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Password verified successfully for user {UserId}", userId);
                return true;
            }

            if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
            {
                _logger.LogWarning("Invalid password provided for user {UserId}", userId);
                return false;
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            _logger.LogError("Failed to verify password for user {UserId}. Status Code: {StatusCode}, Error: {Error}", 
                userId, response.StatusCode, errorContent);
            return new Error<string>(errorContent);
        }

        public async Task<OneOf<LogtoUserProfileDto, Error<string>>> GetUserProfileAsync(string userId)
        {
            var client = _httpClientFactory.CreateClient("LogtoClient");
            var requestUrl = $"/api/users/{userId}";

            var response = await client.GetAsync(requestUrl);
            var ccc = await response.Content.ReadAsStringAsync();
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadFromJsonAsync<LogtoUserProfileDto>();
                if (content != null)
                {
                    _logger.LogInformation("User profile retrieved successfully for user {UserId}", userId);
                    return content;
                }
                return new Error<string>("Failed to deserialize user profile");
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            _logger.LogError("Failed to retrieve user profile for user {UserId}. Status Code: {StatusCode}, Error: {Error}", 
                userId, response.StatusCode, errorContent);
            return new Error<string>($"Failed to retrieve user profile. Status Code: {response.StatusCode}, Error: {errorContent}");
        }

      
    }

    public class UpdateUserPasswordDto
    {
        [JsonPropertyName("currentPassword")]
        public string? CurrentPassword { get; set; }

        [JsonPropertyName("newPassword")]
        public string? NewPassword { get; set; }

        [JsonPropertyName("confirmPassword")]
        public string? ConfirmPassword { get; set; }
    }
    

    public class UpdateUserProfileDto
    {
        [JsonPropertyName("primaryEmail")]
        public string? PrimaryEmail { get; set; }

        [JsonPropertyName("primaryPhone")]
        public string? PrimaryPhone { get; set; }

        [JsonPropertyName("name")]
        public string? Name { get; set; }
    }

    public class LogtoUserProfileDto
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("username")]
        public string Username { get; set; }

        [JsonPropertyName("primaryEmail")]
        public string PrimaryEmail { get; set; }

        [JsonPropertyName("primaryPhone")]
        public string PrimaryPhone { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("avatar")]
        public string Avatar { get; set; }

        [JsonPropertyName("customData")]
        public Dictionary<string, object> CustomData { get; set; }

        [JsonPropertyName("identities")]
        public Dictionary<string, IdentityDto> Identities { get; set; }

        [JsonPropertyName("lastSignInAt")]
        public long LastSignInAt { get; set; }

        [JsonPropertyName("createdAt")]
        public long CreatedAt { get; set; }

        [JsonPropertyName("updatedAt")]
        public long UpdatedAt { get; set; }

        [JsonPropertyName("profile")]
        public Dictionary<string, object> Profile { get; set; }

        [JsonPropertyName("applicationId")]
        public string ApplicationId { get; set; }

        [JsonPropertyName("isSuspended")]
        public bool IsSuspended { get; set; }

        [JsonPropertyName("hasPassword")]
        public bool HasPassword { get; set; }
    }

    public class IdentityDto
    {
        public string UserId { get; set; }
        public Dictionary<string, object> Details { get; set; }
    }
}