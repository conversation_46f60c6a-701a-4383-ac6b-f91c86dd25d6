﻿using CouponApp.Server.Data;
using CouponApp.Server.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;

namespace CouponApp.Server.Services;

public class PermissionService
{
    private readonly ApplicationDbContext _context;
    private readonly IMemoryCache _cache;

    public PermissionService(ApplicationDbContext context, IMemoryCache cache)
    {
        _context = context;
        _cache = cache;
    }

    public PermissionChecker ForUser(string userId)
    {
        return new PermissionChecker(_context, _cache, userId);
    }

    public class PermissionChecker
    {
        private readonly ApplicationDbContext _context;
        private readonly IMemoryCache _cache;
        private readonly string _userId;

        public PermissionChecker(ApplicationDbContext context, IMemoryCache cache, string userId)
        {
            _context = context;
            _cache = cache;
            _userId = userId;
        }

        public async Task<bool> CanWriteCampaign(string campaignId)
        {//TODO
            return true;  /*await _context.CampaignMembers
                .Where(c => c.CampaignId.ToString() == campaignId)
                .AnyAsync(c => c.UserProfileId == _userId);*/
        }

        public async Task<bool> CanReadOrganization(string organizationId)
        {
            return true; /* await _context.Organizations
                .AnyAsync(o => o.Id.ToString() == organizationId && o.Members.Any(m => m.UserProfileId == _userId));*/
        }
        
        public async Task<bool> CanWriteOrganization(string organizationId)
        {
            return await _context.Organizations
                .AnyAsync(o => o.Id.ToString() == organizationId && o.Members.Any(m => m.UserProfileId == _userId && m.Role == UserOrganizationRole.Owner));
        }    
        public async Task<bool> CanDeleteOrganizationMember(string organizationId)
        {
            return await _context.Organizations
                .AnyAsync(o => o.Id.ToString() == organizationId && o.Members.Any(m => m.UserProfileId == _userId && m.Role == UserOrganizationRole.Owner));
        }
    }
}