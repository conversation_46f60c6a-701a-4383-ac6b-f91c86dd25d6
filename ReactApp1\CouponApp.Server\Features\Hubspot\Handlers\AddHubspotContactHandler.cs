﻿// File: Features/Hubspot/Handlers/AddHubspotContactHandler.cs

using CouponApp.Server.Features.Campaigns;
using CouponApp.Server.Features.Organizations;
using CouponApp.Server.Services.Integrations;
using Mediator;
using OneOf;
using OneOf.Types;
using System.Text.Json;
using CouponApp.Server.Data;
using CouponApp.Server.Services;
using Microsoft.EntityFrameworkCore;

namespace CouponApp.Server.Features.Hubspot.Handlers;

public class AddHubspotContactHandler : IRequestHandler<AddHubspotContactCommand, OneOf<Success, Error<string>>>
{
    private readonly IMediator _mediator;
    private readonly ApplicationDbContext _context;
    private readonly INangoProxyClient _nangoProxyClient;
    private readonly ILogger<AddHubspotContactHandler> _logger;

    public AddHubspotContactHandler(
        IMediator mediator,
        INangoProxyServiceFactory nangoProxyServiceFactory,
        ILogger<AddHubspotContactHandler> logger, ApplicationDbContext context)
    {
        _mediator = mediator;
        _nangoProxyClient = nangoProxyServiceFactory.Create(IntegrationType.Hubspot);
        _logger = logger;
        _context = context;
    }

    public async ValueTask<OneOf<Success, Error<string>>> Handle(AddHubspotContactCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var campaign = await _context.Campaigns.FirstOrDefaultAsync(c => c.Id == request.CampaignId, cancellationToken);
            if (campaign == null)
            {
                return new Error<string>("Campaign not found");
            }

            var integrationResult = await _mediator.Send(new GetIntegrationByIdQuery(campaign.WorkspaceId,IntegrationType.Hubspot));
            if (!integrationResult.TryPickT0(out var integration, out var _))
            {
                return new Error<string>("HubSpot integration not found. Please create a HubSpot integration first.");
            }

            var payload = new
            {
                properties = request.ContactProperties
            };

            var endpoint = "crm/v3/objects/contacts";
            var result = await _nangoProxyClient.PostAsync<JsonElement>(endpoint, integration.ConnectionId, payload);

            return result.Match<OneOf<Success, Error<string>>>(
                success => new Success(),
                error => new Error<string>($"Failed to add contact to HubSpot: {error.Value}")
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding contact to HubSpot");
            return new Error<string>($"Error adding contact to HubSpot: {ex.Message}");
        }
    }
}