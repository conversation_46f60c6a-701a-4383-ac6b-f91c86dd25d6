﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace CouponApp.Server.Models.DTOs.Logto;

public class LogtoWebhookData
{
    [JsonPropertyName("result")]
    public string Result { get; set; }
}

public class LogtoWebhookRequest
{
    [JsonPropertyName("hookId")]
    public string HookId { get; set; }

    [JsonPropertyName("event")]
    public string Event { get; set; }

    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; }

    [JsonPropertyName("sessionId")]
    public string SessionId { get; set; }

    [JsonPropertyName("userAgent")]
    public string UserAgent { get; set; }

    [JsonPropertyName("ip")]
    public string Ip { get; set; }

    [JsonPropertyName("path")]
    public string Path { get; set; }

    [JsonPropertyName("method")]
    public string Method { get; set; }

    [JsonPropertyName("status")]
    public int Status { get; set; }

    [JsonPropertyName("params")]
    public Dictionary<string, string> Params { get; set; }

    [JsonPropertyName("data")]
    public JsonElement Data { get; set; } // Use JsonElement for flexible parsing
    
    [JsonPropertyName("user")]
    public UserEntity? User { get; set; } // Use JsonElement for flexible parsing
}

public class UserEntity
{
    [JsonPropertyName("id")]
    public string Id { get; set; }

    [JsonPropertyName("username")]
    public string Username { get; set; }

    [JsonPropertyName("primaryEmail")]
    public string PrimaryEmail { get; set; }

    [JsonPropertyName("primaryPhone")]
    public string PrimaryPhone { get; set; }

    [JsonPropertyName("name")]
    public string? Name { get; set; }

    [JsonPropertyName("avatar")]
    public string Avatar { get; set; }

    [JsonPropertyName("customData")]
    public JsonElement? CustomData { get; set; }

    [JsonPropertyName("identities")]
    public JsonElement? Identities { get; set; }

    // [JsonPropertyName("lastSignInAt")]
    // public string LastSignInAt { get; set; }
    //
    // [JsonPropertyName("createdAt")]
    // public string CreatedAt { get; set; }

    [JsonPropertyName("applicationId")]
    public string ApplicationId { get; set; }

    [JsonPropertyName("isSuspended")]
    public bool IsSuspended { get; set; }
}