﻿using System.Text.Json;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Services.Integrations;

public interface INangoProxyClient
{
    Task<OneOf<T, Error<string>>> GetAsync<T>(string endpoint, string connectionId);
    Task<OneOf<T, Error<string>>> PostAsync<T>(string endpoint, string connectionId, object content);
}

public  class NangoProxyClient : INangoProxyClient
{
    protected readonly INangoClient _nangoClient;
    protected readonly NangoRequestConfig _defaultConfig;

    public NangoProxyClient(INangoClient nangoClient, string providerConfigKey, string baseUrlOverride = null)
    {
        _nangoClient = nangoClient;
        _defaultConfig = new NangoRequestConfig(
            providerConfigKey: providerConfigKey,
            baseUrlOverride: baseUrlOverride
        );
    }

    public Task<OneOf<T, Error<string>>> GetAsync<T>(string endpoint, string connectionId = null)
    {
        endpoint = Path.Combine("proxy", endpoint);
        var config = CreateRequestConfig(connectionId);
        return _nangoClient.GetAsync<T>(endpoint, config);
    }

    public Task<OneOf<T, Error<string>>> PostAsync<T>(string endpoint, string connectionId, object content)
    {
        endpoint = Path.Combine("proxy", endpoint);
        var config = CreateRequestConfig(connectionId);
        return _nangoClient.PostAsync<T>(endpoint, content, config);
    }

    public Task<OneOf<T, Error<string>>> DeleteAsync<T>(string endpoint, string connectionId = null)
    {
        endpoint = Path.Combine("proxy", endpoint);
        var config = CreateRequestConfig(connectionId);
        return _nangoClient.DeleteAsync<T>(endpoint, config);
    }

    private NangoRequestConfig CreateRequestConfig(string connectionId)
    {
        return new NangoRequestConfig(
            connectionId: connectionId,
            providerConfigKey: _defaultConfig.ProviderConfigKey,
            baseUrlOverride: _defaultConfig.BaseUrlOverride
        );
    }
}
