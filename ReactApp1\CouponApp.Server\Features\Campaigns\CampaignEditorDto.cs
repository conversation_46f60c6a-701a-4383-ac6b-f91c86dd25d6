﻿using CouponApp.Server.Models.Entities;
using CouponApp.Server.Models.Enums;

namespace CouponApp.Server.Models.DTOs;

public record PublicCampaignDto
{
    public CampaignConfigDto Config { get; init; } = default!;
    public Guid Id { get; init; }
    public bool IsPublished { get; init; }
    public bool? HasNoCouponsLeft {get; init;}
}

public record CampaignEditorDto
{
    public Guid Id { get; init; }
    public bool IsPublished { get; init; }
    public CampaignConfigDto Config { get; init; } = default!;
    public DateTime CreatedAt { get; init; }
    public DateTime? UpdatedAt { get; init; }
    public string CreatedByUserId { get; set; }
    public string Name { get; init; }
    public string WorkspaceId { get; init; }
    public string Slug { get; init; }
    public CampaignDomainSettingsDto? CampaignDomainSettings { get; init; }
    
    public bool? HasNoCouponsLeft {get; init;}
}
public record CampaignMiniDto
{
    public Guid Id { get; init; }
    public bool IsPublished { get; init; }
    public DateTime CreatedAt { get; init; }
    public DateTime? UpdatedAt { get; init; }
    public string Name { get; init; }
    public bool? HasNoCouponsLeft {get; init;}
    
    public string WorkspaceId { get; init; }
}

public record CampaignCreateDto
{
    public string Name { get; init; }
    public Guid OrganizationId { get; init; }
    public CampaignConfigDto Config { get; init; } = default!;
}

public record CampaignUpdateDto
{
    public CampaignSaveMode SaveMode { get; init; }
    public CampaignConfigDto Config { get; init; } = default!;
    
    public string? Name { get; init; }
    
    public CampaignDomainSettingsDto? CampaignDomainSettings { get; init; }
    
  
    
}

public record  CampaignDomainSettingsDto {
    public string? CustomDomainId { get; init; }
    public string? Slug { get; init; }
}

public record CampaignAssetDto
{
    public string Id { get; set; }
    public string FileUrl { get; set; }
}

public record CampaignFlowNodeDto
{
    public string Id { get; set; }
    public string Type { get; set; }
    public Point Position { get; set; }
    public List<string> Connections { get; set; }
    public string? ElseConnection { get; set; }
    public Dictionary<string, object> Payload { get; set; }
}


