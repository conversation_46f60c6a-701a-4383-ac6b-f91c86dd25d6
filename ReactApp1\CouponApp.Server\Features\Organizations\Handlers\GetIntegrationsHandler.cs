﻿// File: ~/CouponApp.Server/Features/Organizations/Handlers/GetIntegrationsHandler.cs

using CouponApp.Server.Data;
using CouponApp.Server.Mappers;
using CouponApp.Server.Models.DTOs;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Organizations.Handlers;

public class GetIntegrationsHandler : IRequestHandler<GetIntegrationsQuery, OneOf<List<OrganizationIntegrationDto>, NotFound>>
{
    private readonly ApplicationDbContext _context;

    public GetIntegrationsHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<List<OrganizationIntegrationDto>, NotFound>> Handle(GetIntegrationsQuery request, CancellationToken cancellationToken)
    {
        var integrations = await _context.OrganizationIntegrations
            .Where(oi => oi.OrganizationId == request.OrganizationId)
            .ToListAsync(cancellationToken);

        return integrations.Select(o => o.ToDto()).ToList();
    }
}