﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CouponApp.Server.Migrations
{
    /// <inheritdoc />
    public partial class OrganizationAssetOptionalOrg : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_OrganizationAssets_Organizations_OrganizationId",
                table: "OrganizationAssets");

            migrationBuilder.AlterColumn<Guid>(
                name: "OrganizationId",
                table: "OrganizationAssets",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AddForeignKey(
                name: "FK_OrganizationAssets_Organizations_OrganizationId",
                table: "OrganizationAssets",
                column: "OrganizationId",
                principalTable: "Organizations",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_OrganizationAssets_Organizations_OrganizationId",
                table: "OrganizationAssets");

            migrationBuilder.AlterColumn<Guid>(
                name: "OrganizationId",
                table: "OrganizationAssets",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_OrganizationAssets_Organizations_OrganizationId",
                table: "OrganizationAssets",
                column: "OrganizationId",
                principalTable: "Organizations",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
