﻿using CouponApp.Server.Data;
using CouponApp.Server.Mappers;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Models.Errors;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.CouponPacks.Handlers;

public class AddCouponsToCouponPackHandler : IRequestHandler<AddCouponsToCouponPackCommand, OneOf<CouponPackDto, NotFound, CouponPackFullError>>
{
    private readonly ApplicationDbContext _context;

    public AddCouponsToCouponPackHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<CouponPackDto, NotFound, CouponPackFullError>> Handle(AddCouponsToCouponPackCommand request, CancellationToken cancellationToken)
    {
        var couponPack = await _context.CouponPacks
            .Include(cp => cp.Coupons)
            .FirstOrDefaultAsync(cp => cp.Id == request.Id, cancellationToken);

        if (couponPack == null)
        {
            return new NotFound();
        }

        if (couponPack.Coupons.Count + request.CouponValues.Count > 1000)
        {
            return new CouponPackFullError();
        }

        var newCoupons = request.CouponValues.Select(value => new Coupon { Value = value }).ToList();
        couponPack.Coupons.AddRange(newCoupons);

        await _context.SaveChangesAsync(cancellationToken);
        return couponPack.ToDto();
    }
}

