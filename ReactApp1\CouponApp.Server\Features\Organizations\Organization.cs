﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CouponApp.Server.Data;

namespace CouponApp.Server.Models.Entities;

public class Organization : BaseEntity
{
    
    [Required]
    public string ShortId { get; set; }
    
    [Required]
    public string Name { get; set; }
    
    public string? LogoUrl { get; set; }


    public ICollection<UserOrganization> Members { get; set; } = new List<UserOrganization>();
    
    public ICollection<OrganizationIntegration> Integrations { get; set; } = new List<OrganizationIntegration>();
    
    public ICollection<OrganizationAsset> OrganizationAssets { get; set; } = new List<OrganizationAsset>();
}
