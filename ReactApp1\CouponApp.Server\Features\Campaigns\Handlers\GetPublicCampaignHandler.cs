﻿using CommunityToolkit.HighPerformance.Helpers;
using CouponApp.Server.Data;
using CouponApp.Server.Extensions;
using CouponApp.Server.Mappers;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Services;
using CouponApp.Server.Services.Integrations;
using Mediator;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Campaigns.Handlers;

public class GetPublicCampaignHandler : IRequestHandler<GetPublicCampaignQuery, OneOf<PublicCampaignDataDto, NotFound>>
{
    private readonly ApplicationDbContext _context;
    private readonly ICampaignSessionService _campaignSessionService;
    private readonly IDistributedCache _cache;

    public GetPublicCampaignHandler(ApplicationDbContext context, ICampaignSessionService campaignSessionService, IDistributedCache cache)
    {
        _context = context;
        _campaignSessionService = campaignSessionService;
        _cache = cache;
    }
    public async ValueTask<OneOf<PublicCampaignDataDto, NotFound>> Handle(GetPublicCampaignQuery request, CancellationToken cancellationToken)
    {
        IQueryable<Campaign> query;
        var hostname = request.Hostname;
        
        if (hostname.StartsWith("localhost"))
        {
            hostname = "play.beakbyte.com";
        }
        
        if(!hostname.StartsWith("play.beakbyte.com")) //TODO Play domain from app settings.
        {
            query = _context.Campaigns.Where(c => c.CampaignDomainSettings.CustomDomain.DomainName == hostname && c.CampaignDomainSettings.Slug == request.IdOrSlug);
        }
        else
        {
             query =  _context.Campaigns.Where(c => c.Id == Guid.Parse(request.IdOrSlug));
        }
        
        
        var campaign = await query.Select(campaign => new
            {
                campaign.Id,
                campaign.IsPublished,
                campaign.Config,
                // AssetUrls = campaign.Workspace.OrganizationAssets.Select(o => new
                // {
                //     o.Id,
                //     // FileUrl = new Uri(new Uri("https://cdn-beakbyte.b-cdn.net"), new Uri(o.FileUrl).AbsolutePath).AbsoluteUri
                //     o.FileUrl
                // }),
                Interations = campaign.Workspace.Integrations.Select(o => new
                {
                    o.Id,
                    o.IntegrationType,
                    o.AdditionalData
                })
            })
            .AsSplitQuery()
            .FirstOrDefaultAsync(cancellationToken);


        if (campaign is null || !campaign.IsPublished)
        {
            return new NotFound();
        }
        
        var hasParticipated = await _campaignSessionService.HasParticipatedInCampaignAsync(campaign.Id, request.Fingerprint);

        var googleAnalyticsMeasurementId = campaign.Interations
            .Where(i => i.IntegrationType == IntegrationType.GoogleAnalytics)
            .Select(i => i.AdditionalData)
            .Select(d => d.TryGetValue("measurementId", out var value) ? value.ToString() : null)
            .FirstOrDefault();
        
        var tiktokAdsPixelId = campaign.Interations
            .Where(i => i.IntegrationType == IntegrationType.TiktokAdsPixel)
            .Select(i => i.AdditionalData)
            .Select(d =>  d.TryGetValue("pixelId", out var value) ? value.ToString() : null)
            .FirstOrDefault();        
        
        var metaPixelId = campaign.Interations
            .Where(i => i.IntegrationType == IntegrationType.MetaPixel)
            .Select(i => i.AdditionalData)
            .Select(d =>  d.TryGetValue("pixelId", out var value) ? value.ToString() : null)
            .FirstOrDefault();

            
        var assetIds = campaign.Config.UsedAssetIds
            .Where(assetId => Guid.TryParse(assetId, out _))
            .Select(assetId => assetId)
            .Select(id => Guid.Parse(id)).ToList();

        var assets = await _context.OrganizationAssets
            .Where(a => assetIds.Contains(a.Id))
            .ToListAsync();


        var assetMappings = campaign.Config.UsedAssetIds
              .Where(assetId => Guid.TryParse(assetId, out _))
              .Select(assetId => Guid.Parse(assetId)).Select(assetId =>
              new CampaignAssetMappingDto
              {
                  Id = assetId.ToString(),
                  Url = assets.FirstOrDefault(a => a.Id == assetId)?.FileUrl ?? ""
              }).ToList();
        
        var campaignDataResponse = new PublicCampaignDataDto
        {
            Campaign = new PublicCampaignDto
            {
                IsPublished = campaign.IsPublished,
                Config = campaign.Config.ToPublicConfigDto(),
                HasNoCouponsLeft = false,
                Id = campaign.Id
            },
            HasParticipated = hasParticipated,
            GoogleAnalyticsMeasurementId = googleAnalyticsMeasurementId,
            TiktokAdsPixelId = tiktokAdsPixelId,
            MetaPixelId = metaPixelId,
             Assets = assetMappings
        };

        //await _cache.SetJsonAsync(key, campaignDataResponse, TimeSpan.FromMinutes(5));
        
        return campaignDataResponse;
    }
}