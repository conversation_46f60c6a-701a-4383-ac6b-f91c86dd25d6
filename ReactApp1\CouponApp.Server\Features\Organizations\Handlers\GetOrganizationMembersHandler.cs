﻿// File: ~/CouponApp.Server/Features/Organizations/Handlers/GetOrganizationMembersHandler.cs

using CouponApp.Server.Data;
using CouponApp.Server.Mappers;
using CouponApp.Server.Models.DTOs;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Organizations.Handlers;

public class GetOrganizationMembersHandler : IRequestHandler<GetOrganizationMembersQuery, OneOf<OrganizationMembersResponse, NotFound>>
{
    private readonly ApplicationDbContext _context;

    public GetOrganizationMembersHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<OrganizationMembersResponse, NotFound>> Handle(GetOrganizationMembersQuery request, CancellationToken cancellationToken)
    {
        var organization = await _context.Organizations
            .Include(o => o.Members)
            .ThenInclude(m => m.UserProfile)
            .FirstOrDefaultAsync(o => o.Id == request.OrganizationId, cancellationToken);

        var invitedMembers = await _context.Invitations
            .Where(i => i.OrganizationId == request.OrganizationId)
            .Where(i => DateTime.UtcNow <= i.ExpiresAt)
            .Select(i => new InvitationDto
            {
                Email = i.Email,
                ExpiresAt = i.ExpiresAt,
                Id = i.Id
            })
            .ToListAsync();
        
        if (organization == null)
        {
            return new NotFound();
        }

        var members = organization.Members;
        
        return new OrganizationMembersResponse
        {
            Members = members.Select(m => m.UserProfile).Select(ApplicationUserMapper.ToDto).ToList(),
            InvitedMembers = invitedMembers
        };
    }
}