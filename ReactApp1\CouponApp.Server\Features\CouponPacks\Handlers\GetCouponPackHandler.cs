﻿using CouponApp.Server.Data;
using CouponApp.Server.Mappers;
using CouponApp.Server.Models.DTOs;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.CouponPacks.Handlers;

public class GetCouponPackHandler : IRequestHandler<GetCouponPackQuery, OneOf<CouponPackDto, NotFound>>
{
    private readonly ApplicationDbContext _context;

    public GetCouponPackHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<CouponPackDto, NotFound>> Handle(GetCouponPackQuery request, CancellationToken cancellationToken)
    {
        var couponPack = await _context.CouponPacks
            .Include(cp => cp.Coupons)
            .FirstOrDefaultAsync(cp => cp.Id == request.Id, cancellationToken);

        if (couponPack == null)
        {
            return new NotFound();
        }

        return couponPack.ToDto();
    }
}