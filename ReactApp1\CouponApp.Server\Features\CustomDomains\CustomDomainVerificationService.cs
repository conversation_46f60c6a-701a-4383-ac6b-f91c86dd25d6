﻿using CouponApp.Server.Data;
using CouponApp.Server.Extensions;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Models.Enums;
using CouponApp.Server.Services;
using Medallion.Threading;
using Microsoft.EntityFrameworkCore;

public class CustomDomainVerificationService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<CustomDomainVerificationService> _logger;
    private readonly ICloudflareService _cloudflareService;
    private readonly IDistributedLockProvider _lockProvider;
    private const string LockKey = "DomainVerificationLock";
    private const int LockTimeoutSeconds = 300; // 5 minutes

    public CustomDomainVerificationService(
        IServiceProvider serviceProvider,
        ILogger<CustomDomainVerificationService> logger, 
        ICloudflareService cloudflareService,
        IDistributedLockProvider lockProvider)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _cloudflareService = cloudflareService;
        _lockProvider = lockProvider;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                // Create a PostgreSQL advisory lock key 
                using (var lockHandle = await _lockProvider.TryAcquireLockAsync(
                    LockKey, 
                    TimeSpan.FromSeconds(LockTimeoutSeconds)))
                {
                    if (lockHandle != null)
                    {
                        try
                        {
                            using var scope = _serviceProvider.CreateScope();
                            var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

                            var pendingDomains = await dbContext.CustomDomains
                                .Where(d => d.Status != CustomDomainStatus.Active && d.Status != CustomDomainStatus.Failed)
                                .ToListAsync(stoppingToken);

                            foreach (var domain in pendingDomains)
                            {
                                await VerifyDomain(domain, dbContext);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error while processing domains");
                        }
                    }
                    else
                    {
                        _logger.LogInformation("Could not acquire lock, another instance is already running");
                    }
                }
                
                // Wait before next iteration
                await Task.Delay(TimeSpan.FromSeconds(60), stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in domain verification service");
                await Task.Delay(TimeSpan.FromSeconds(20), stoppingToken);
            }
        }
    }

    private async Task VerifyDomain(CustomDomain domain, ApplicationDbContext dbContext)
    {
        try
        {
            Console.WriteLine("Checking domain: " + domain.DomainName);

            if (domain.Status == CustomDomainStatus.Initializing)
            {
                var response = await _cloudflareService.CreateHostnameAsync(domain.DomainName);
                
                response.Switch(hostname =>
                {
                    domain.CloudflareHostnameId = hostname.Id;
                    domain.Status = CustomDomainStatus.DnsVerifying;
                }, error =>
                {
                    domain.ErrorMessage = error.Message;
                    domain.Status = CustomDomainStatus.Failed;
                });
                
                await dbContext.SaveChangesAsync();
                return;
            }


            if (domain.Status is CustomDomainStatus.DnsVerifying)
            {
                var response = await _cloudflareService.GetHostnameAsync(domain.CloudflareHostnameId);
                response.Switch(successResult =>
                {
                    if (successResult.Status == HostnameStatus.Active)
                    {
                        domain.Status = CustomDomainStatus.SslProvisioning;
                    }

                }, error =>
                {
                    domain.ErrorMessage = error.Message;
                    domain.Status = CustomDomainStatus.Failed;
                });
                await dbContext.SaveChangesAsync();
                return;
            }
            
            // // Verify DNS records
            if (domain.Status is  CustomDomainStatus.SslProvisioning)
            {
                var response = await _cloudflareService.GetHostnameAsync(domain.CloudflareHostnameId);
                response.Switch(successResult =>
                {
                    domain.Status = successResult.Ssl.Status switch
                    {
                        SslStatusEnum.PendingValidation or SslStatusEnum.PendingIssuance
                            or SslStatusEnum.PendingDeployment => CustomDomainStatus.SslProvisioning,
                        SslStatusEnum.Active => CustomDomainStatus.Active,
                        _ => domain.Status
                    };
                }, error =>
                {
                    domain.ErrorMessage = error.Message;
                    domain.Status = CustomDomainStatus.Failed;
                });
                 
                await dbContext.SaveChangesAsync();
            }
        }
        catch (Exception ex)
        {
            domain.Status = CustomDomainStatus.Failed;
            domain.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Error verifying domain {DomainName}", domain.DomainName);
        }

    }
}

