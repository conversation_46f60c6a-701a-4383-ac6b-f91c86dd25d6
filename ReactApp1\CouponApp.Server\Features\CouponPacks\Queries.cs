﻿using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Services;
using Mediator;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.CouponPacks;

public record GetCouponPackQuery(Guid Id) : IRequest<OneOf<CouponPackDto, NotFound>>;

public record GetCampaignCouponPacksQuery(Guid CampaignId) : IRequest<IEnumerable<CouponPackMiniDto>>;


public record GetCouponPackCouponsLeftCountQuery(Guid CouponPackId) : IRequest<CouponPackCountDto>;