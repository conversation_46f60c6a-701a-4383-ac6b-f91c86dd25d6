﻿using CouponApp.Server.Data;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Services;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Invitations.Handlers;

public class AcceptInvitationHandler : IRequestHandler<AcceptInvitationCommand, OneOf<Success, NotFound, InvalidInvitation>>
{
    private readonly ApplicationDbContext _context;

    public AcceptInvitationHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<Success, NotFound, InvalidInvitation>> Handle(AcceptInvitationCommand request, CancellationToken cancellationToken)
    {
        var invitation = await _context.Invitations
            .Include(i => i.Organization)
            .ThenInclude(o => o.Members)
            .FirstOrDefaultAsync(i => i.InvitationToken == request.Token, cancellationToken);

        if (invitation == null)
        {
            return new NotFound();
        }

        if (invitation.ExpiresAt < DateTime.UtcNow)
        {
            return new InvalidInvitation("Invitation has expired");
        }

        var user = await _context.UserProfiles.FindAsync(new object[] { request.UserId }, cancellationToken);
        if (user == null)
        {
            return new NotFound();
        }
        
        if(invitation.Organization.Members.Any(m => m.UserProfileId == user.Id))
        {
            return new InvalidInvitation("User is already a member of the organization");
        }

        invitation.Organization.Members.Add(new UserOrganization
        {
            UserProfileId = user.Id,
            OrganizationId = invitation.OrganizationId,
        });
        
        _context.Invitations.Remove(invitation);
        await _context.SaveChangesAsync(cancellationToken);

        return new Success();
    }
}