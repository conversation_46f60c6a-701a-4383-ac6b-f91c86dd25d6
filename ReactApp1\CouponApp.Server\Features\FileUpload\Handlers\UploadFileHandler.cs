﻿using Mediator;
using OneOf;
using OneOf.Types;
using Minio;
using Minio.DataModel.Args;

namespace CouponApp.Server.Features.FileUpload.Handlers;

public class UploadFileHandler : IRequestHandler<UploadFileCommand, OneOf<string, Error<string>>>
{
    private readonly IMinioClient _minioClient;
    private readonly string _bucketName;
    private readonly string _minioPublicUrl;

    public UploadFileHandler(IConfiguration configuration, IMinioClient minioClient)
    {
        _minioClient = minioClient;
        var minioEndpoint = configuration["Minio:Endpoint"];
        var minioAccessKey = configuration["Minio:AccessKey"];
        var minioSecretKey = configuration["Minio:SecretKey"];
        _bucketName = configuration["Minio:BucketName"];
        _minioPublicUrl = configuration["Minio:PublicUrl"];


    }

    public async ValueTask<OneOf<string, Error<string>>> Handle(UploadFileCommand request, CancellationToken cancellationToken)
    {
        if (request.File == null || request.File.Length == 0)
            return new Error<string>("File is empty");

        var extension = Path.GetExtension(request.File.FileName);
        var allowedExtensions = new[] {".png", ".jpg", ".jpeg", ".webp", ".avif",".mp4", ".gif", ".svg", ".riv"};
        if(allowedExtensions.All(x => x != extension))
            return new Error<string>($"File extension {extension} is not allowed");

        var fileName = $"{Guid.NewGuid()}{extension}";

        using var stream = request.File.OpenReadStream();
        await _minioClient.PutObjectAsync(new PutObjectArgs()
            .WithBucket(_bucketName)
            .WithObject(fileName)
            .WithStreamData(stream)
            .WithObjectSize(stream.Length)
            .WithContentType(request.File.ContentType));

        return $"{_minioPublicUrl}/{_bucketName}/{fileName}";
    }
}