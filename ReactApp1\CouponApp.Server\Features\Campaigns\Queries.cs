﻿using CouponApp.Server.Models;
using CouponApp.Server.Models.DTOs;
using Mediator;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Campaigns;

public record GetCampaignForEditorQuery(Guid Id) : IRequest<OneOf<CampaignEditorDto, NotFound>>;

public record GetPublicCampaignQuery(string IdOrSlug, SessionFingerprint Fingerprint, string Hostname) : IRequest<OneOf<PublicCampaignDataDto, NotFound>>;

public record GetCampaignsByOrganizationIdQuery(Guid WorkspaceId) 
    : IRequest<OneOf<IEnumerable<CampaignMiniDto>, Error<string>>>;

