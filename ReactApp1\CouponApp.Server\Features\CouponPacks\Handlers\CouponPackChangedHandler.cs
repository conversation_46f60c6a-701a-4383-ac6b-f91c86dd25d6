﻿using CouponApp.Server.Data;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Services;
using Mediator;
using Microsoft.EntityFrameworkCore;

namespace CouponApp.Server.Features.CouponPacks.Handlers;

public class CouponPackChangedHandler : INotificationHandler<CouponPackChangedNotification>
{
    private readonly ApplicationDbContext _context;
    private readonly IMediator _mediator;

    public CouponPackChangedHandler(
        ApplicationDbContext context,  IMediator mediator)
    {
        _context = context;
        _mediator = mediator;
    }

    public async ValueTask Handle(CouponPackChangedNotification notification, CancellationToken cancellationToken)
    {
        await CheckThresholdsReachedAsync(notification.CouponPackId, cancellationToken);
    }

    private async Task CheckThresholdsReachedAsync(Guid couponPackId, CancellationToken cancellationToken)
    {
        var couponPack = await _context.CouponPacks
            .Where(cp => cp.Id == couponPackId)
            .Select(cp => new 
            {
                cp.Id, 
                cp.NotifyThreshold, 
                cp.InterruptThreshold, 
                 cp.CampaignId, 
                 cp.Campaign.WorkspaceId,
                // cp.Campaign.Project.Organization.Owner.Email, 
                cp.Name, 
                CampaignName = cp.Campaign.Name
            })
            .FirstOrDefaultAsync(cancellationToken);

        if (couponPack?.NotifyThreshold == null)
        {
            return;
        }

        var remainingCoupons = await _context.Coupons.CountAsync(c => c.CouponPackId == couponPackId, cancellationToken);

        if (remainingCoupons <= couponPack.InterruptThreshold)
        {
            await _mediator.Publish(new CampaignHasNoCouponsLeftNotification(couponPack.CampaignId), cancellationToken);
        }

        if (remainingCoupons <= couponPack.NotifyThreshold)
        {
            var ownerEmail = await _context.UserOrganizations.Where(uo => uo.OrganizationId == couponPack.WorkspaceId && uo.Role == UserOrganizationRole.Owner)
                .Select(uo => uo.UserProfile.Email)
                .FirstOrDefaultAsync(cancellationToken);
            
            
            await _mediator.Publish(new CouponPackReachedNotificationTreshold(ownerEmail, couponPack.CampaignName, couponPack.Name, remainingCoupons, couponPack.Id.ToString(), couponPack.CampaignId.ToString()), cancellationToken);
        }
    }
}