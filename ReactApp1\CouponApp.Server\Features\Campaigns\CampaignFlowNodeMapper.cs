﻿using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;

namespace CouponApp.Server.Mappers;

public static class CampaignFlowNodeMapper
{
    
    public static CampaignFlowNodeDto ToDto(this CampaignFlowNode node) => new()
    {
        Id = node.Id,
        Type = node.Type,
        Position = node.Position,
        Connections = node.Connections,
        Payload = node.Payload,
        ElseConnection = node.ElseConnection
    };
    
}