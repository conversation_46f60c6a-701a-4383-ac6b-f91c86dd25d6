﻿using CouponApp.Server.Data;
using CouponApp.Server.Mappers;
using CouponApp.Server.Models.DTOs;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Organizations.Handlers;

public class GetIntegrationByIdHandler : IRequestHandler<GetIntegrationByIdQuery, OneOf<OrganizationIntegrationDto, NotFound>>
{
    private readonly ApplicationDbContext _context;

    public GetIntegrationByIdHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<OrganizationIntegrationDto, NotFound>> Handle(GetIntegrationByIdQuery request, CancellationToken cancellationToken)
    {
        var integration = await _context.OrganizationIntegrations
            .FirstOrDefaultAsync(org => org.OrganizationId == request.OrganizationId && org.IntegrationType == request.IntegrationType);
        
        if (integration == null)
        {
            return new NotFound();
        }
        
        return integration.ToDto();
    }
}