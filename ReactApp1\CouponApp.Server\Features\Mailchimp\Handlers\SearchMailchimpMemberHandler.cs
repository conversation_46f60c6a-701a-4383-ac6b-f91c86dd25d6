﻿using CouponApp.Server.Models.DTOs.Mailchimp;
using CouponApp.Server.Services;
using CouponApp.Server.Services.Integrations;
using Mediator;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Mailchimp.Handlers;

public class SearchMailchimpMemberHandler : IRequestHandler<SearchMailchimpMemberQuery, OneOf<MailchimpSearchMemberResponseDto, Error<string>>>
{
    private readonly INangoProxyClient _nangoProxyClient;

    public SearchMailchimpMemberHandler(INangoProxyServiceFactory nangoProxyServiceFactory)
    {
        _nangoProxyClient = nangoProxyServiceFactory.Create(IntegrationType.Mailchimp);
    }

    public async ValueTask<OneOf<MailchimpSearchMemberResponseDto, Error<string>>> Handle(SearchMailchimpMemberQuery request, CancellationToken cancellationToken)
    {
        var queryParams = new Dictionary<string, string>
        {
            ["list_id"] = request.ListId,
            ["query"] = request.EmailAddress
        };

        var queryString = string.Join("&", queryParams.Select(kv => $"{kv.Key}={Uri.EscapeDataString(kv.Value)}"));
        var endpoint = $"search-members?{queryString}";

        return await _nangoProxyClient.GetAsync<MailchimpSearchMemberResponseDto>(endpoint, request.ConnectionId);
    }
}