﻿namespace CouponApp.Server.Models.DTOs.Organization;

public record OrganizationAssetDto
{
    public Guid Id { get; init; }
    public string FileName { get; init; }
    public string FileUrl { get; init; }
    public long FileSize { get; init; } // In bytes
    public string ContentType { get; init; }
    public DateTime CreatedAt { get; init; }
}

public record AssetsResponseDto
{
    public List<OrganizationAssetDto> Assets { get; set; }
    public long AvailableStorage { get; set; }
    public long UsedStorage { get; set; }
}