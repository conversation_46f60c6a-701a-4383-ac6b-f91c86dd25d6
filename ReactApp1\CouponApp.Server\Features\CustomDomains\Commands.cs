﻿using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.DTOs.Requests;
using CouponApp.Server.Models.Enums;
using Mediator;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.CustomDomains;

public record CreateCustomDomainCommand(Guid OrganizationId, CreateCustomDomainRequest Dto) 
    : IRequest<OneOf<CustomDomainDto, Error<string>>>;

public record DeleteCustomDomainCommand(Guid OrganizationId, Guid DomainId) 
    : IRequest<OneOf<Success, NotFound>>;

public record VerifyCustomDomainCommand(Guid OrganizationId, Guid DomainId) 
    : IRequest<OneOf<CustomDomainDto, NotFound, Error<string>>>;

public record UpdateCustomDomainStatusCommand(Guid DomainId, CustomDomainStatus NewStatus, string? ErrorMessage = null) 
    : IRequest<OneOf<Success, NotFound>>;

