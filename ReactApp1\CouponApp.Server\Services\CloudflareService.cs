﻿using System.Net.Http.Json;
using System.Text.Json.Serialization;
using Microsoft.Extensions.Options;
using OneOf;
using OneOf.Types;
using Microsoft.Extensions.Logging;

namespace CouponApp.Server.Services
{
    public class CloudflareConfiguration
    {
        public string ApiKey { get; set; }
        public string ZoneId { get; set; }
        public string AccountId { get; set; }
    }

    // Define a structured error model
    public class ServiceError
    {
        public string Code { get; set; }
        public string Message { get; set; }

        public ServiceError(string code, string message)
        {
            Code = code;
            Message = message;
        }
    }

    public interface ICloudflareService
    {
        Task<OneOf<CloudflareHostname, ServiceError>> CreateHostnameAsync(string domainName);
        Task<OneOf<CloudflareHostname, ServiceError>> GetHostnameAsync(string hostnameId);
        Task<OneOf<List<CloudflareHostname>, ServiceError>> ListHostnamesAsync();
        Task<OneOf<Success, ServiceError>> DeleteHostnameAsync(string hostnameId);
    }

    public class CloudflareService : ICloudflareService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<CloudflareService> _logger;
        private readonly string _zoneId;

        public CloudflareService(
            HttpClient httpClient,
            IOptions<CloudflareConfiguration> configuration,
            ILogger<CloudflareService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _zoneId = configuration.Value.ZoneId;
        }

        public async Task<OneOf<CloudflareHostname, ServiceError>> CreateHostnameAsync(string domainName)
        {
            try
            {
                var request = new CreateHostnameRequest
                {
                    Hostname = domainName,
                    SslSettings = new SslSettings
                    {
                        BundleMethod = "ubiquitous",
                        Type = "dv",
                        ValidationMethod = "http",
                        Settings = new Settings
                        {
                            MinTlsVersion = "1.2"
                        }
                    }
                };

                var response = await _httpClient.PostAsJsonAsync($"zones/{_zoneId}/custom_hostnames", request);

                if (!response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to create hostname. Status: {StatusCode} Content: {Content}", response.StatusCode, responseContent);
                    return new ServiceError("CreateFailed", "Failed to create hostname. Please try again later:  " + responseContent);
                }

                var cloudflareResponse = await response.Content.ReadFromJsonAsync<CloudflareResponse<CloudflareHostname>>();
                if (cloudflareResponse == null || !cloudflareResponse.Success)
                {
                    var errorCodes = cloudflareResponse?.Errors?.Select(e => e.Code) ?? new List<int> { 0 };
                    _logger.LogError("Failed to create hostname. Error Codes: {ErrorCodes}", string.Join(", ", errorCodes));
                    return new ServiceError("CreateFailed", "Failed to create hostname due to a service error.");
                }

                return cloudflareResponse.Result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception while creating hostname for domain {DomainName}", domainName);
                return new ServiceError("InternalError", "An unexpected error occurred. Please contact support.");
            }
        }

        public async Task<OneOf<CloudflareHostname, ServiceError>> GetHostnameAsync(string hostnameId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"zones/{_zoneId}/custom_hostnames/{hostnameId}");

                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return new ServiceError("NotFound", "Hostname not found.");
                }

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError("Failed to get hostname. Status: {StatusCode}", response.StatusCode);
                    return new ServiceError("GetFailed", "Failed to retrieve hostname. Please try again later.");
                }

                var cloudflareResponse = await response.Content.ReadFromJsonAsync<CloudflareResponse<CloudflareHostname>>();
                if (cloudflareResponse == null || !cloudflareResponse.Success)
                {
                    var errorMessages = cloudflareResponse?.Errors?.Select(e => e.Code) ?? new List<int> { 0 };
                    _logger.LogError("Failed to get hostname. Error Codes: {ErrorCodes}", string.Join(", ", errorMessages));
                    return new ServiceError("GetFailed", "Failed to retrieve hostname due to a service error.");
                }

                return cloudflareResponse.Result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception while getting hostname {HostnameId}", hostnameId);
                return new ServiceError("InternalError", "An unexpected error occurred. Please contact support.");
            }
        }

        public async Task<OneOf<List<CloudflareHostname>, ServiceError>> ListHostnamesAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync($"zones/{_zoneId}/custom_hostnames");

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError("Failed to list hostnames. Status: {StatusCode}", response.StatusCode);
                    return new ServiceError("ListFailed", "Failed to retrieve hostnames. Please try again later.");
                }

                var cloudflareResponse = await response.Content.ReadFromJsonAsync<CloudflareResponse<List<CloudflareHostname>>>();
                if (cloudflareResponse == null || !cloudflareResponse.Success)
                {
                    var errorCodes = cloudflareResponse?.Errors?.Select(e => e.Code) ?? new List<int> { 0 };
                    _logger.LogError("Failed to list hostnames. Error Codes: {ErrorCodes}", string.Join(", ", errorCodes));
                    return new ServiceError("ListFailed", "Failed to retrieve hostnames due to a service error.");
                }

                return cloudflareResponse.Result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception while listing hostnames");
                return new ServiceError("InternalError", "An unexpected error occurred. Please contact support.");
            }
        }

        public async Task<OneOf<Success, ServiceError>> DeleteHostnameAsync(string hostnameId)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"zones/{_zoneId}/custom_hostnames/{hostnameId}");

                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    // Consider the hostname already deleted
                    return new Success();
                }

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError("Failed to delete hostname. Status: {StatusCode}", response.StatusCode);
                    return new ServiceError("DeleteFailed", "Failed to delete hostname. Please try again later.");
                }

                return new Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception while deleting hostname {HostnameId}", hostnameId);
                return new ServiceError("InternalError", "An unexpected error occurred. Please contact support.");
            }
        }

        // Helper methods or additional logic can be added here if necessary
    }

    // Supporting Classes
    public class CloudflareResponse<T>
    {
        [JsonPropertyName("success")]
        public bool Success { get; set; }

        [JsonPropertyName("errors")]
        public List<CloudflareError> Errors { get; set; }

        [JsonPropertyName("result")]
        public T Result { get; set; }
    }

    public class CloudflareError
    {
        [JsonPropertyName("code")]
        public int Code { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; }
    }

    public class CloudflareHostname
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("hostname")]
        public string Hostname { get; set; }

        [JsonPropertyName("ssl")]
        public SslStatus Ssl { get; set; }

        [JsonPropertyName("status")]
        public string Status { get; set; }
    }

    public class SslStatus
    {
        [JsonPropertyName("status")]
        public string Status { get; set; }

        [JsonPropertyName("method")]
        public string Method { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }
    }

    public class CreateHostnameRequest
    {
        [JsonPropertyName("hostname")]
        public string Hostname { get; set; }

        [JsonPropertyName("ssl")]
        public SslSettings SslSettings { get; set; }
    }

    public class SslSettings
    {
        [JsonPropertyName("method")]
        public string ValidationMethod { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }

        [JsonPropertyName("bundle_method")]
        public string BundleMethod { get; set; }

        [JsonPropertyName("settings")]
        public Settings Settings { get; set; }
    }

    public class Settings
    {
        [JsonPropertyName("min_tls_version")]
        public string MinTlsVersion { get; set; }
    }

    public class HostnameStatus
    {
        public const string Unknown = "unknown";
        public const string Active = "active";
        public const string Pending = "pending";
        public const string ActiveRedeploying = "active_redeploying";
        public const string Moved = "moved";
        public const string PendingDeletion = "pending_deletion";
        public const string Deleted = "deleted";
        public const string PendingBlocked = "pending_blocked";
        public const string PendingMigration = "pending_migration";
        public const string PendingProvisioned = "pending_provisioned";
        public const string TestPending = "test_pending";
        public const string TestActive = "test_active";
        public const string TestActiveApex = "test_active_apex";
        public const string TestBlocked = "test_blocked";
        public const string TestFailed = "test_failed";
        public const string Provisioned = "provisioned";
        public const string Blocked = "blocked";
    }

    public class SslStatusEnum
    {
        public const string Unknown = "unknown";
        public const string Initializing = "initializing";
        public const string PendingValidation = "pending_validation";
        public const string Deleted = "deleted";
        public const string PendingIssuance = "pending_issuance";
        public const string PendingDeployment = "pending_deployment";
        public const string PendingDeletion = "pending_deletion";
        public const string PendingExpiration = "pending_expiration";
        public const string Expired = "expired";
        public const string Active = "active";
        public const string InitializingTimedOut = "initializing_timed_out";
        public const string ValidationTimedOut = "validation_timed_out";
        public const string IssuanceTimedOut = "issuance_timed_out";
        public const string DeploymentTimedOut = "deployment_timed_out";
        public const string DeletionTimedOut = "deletion_timed_out";
        public const string PendingCleanup = "pending_cleanup";
        public const string StagingDeployment = "staging_deployment";
        public const string StagingActive = "staging_active";
        public const string Deactivating = "deactivating";
        public const string Inactive = "inactive";
        public const string BackupIssued = "backup_issued";
        public const string HoldingDeployment = "holding_deployment";
    }
}