﻿using CouponApp.Server.Data;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.DTOs.Organization;
using CouponApp.Server.Models.Errors;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;

namespace CouponApp.Server.Features.OrganizationAssets.Handlers;

public class GetOrganizationAssetsHandler
    : IRequestHandler<GetOrganizationAssetsQuery, OneOf<AssetsResponseDto, UnauthorizedError>>
{
    private readonly ApplicationDbContext _context;
    
    

    public GetOrganizationAssetsHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<AssetsResponseDto, UnauthorizedError>> Handle(
        GetOrganizationAssetsQuery request, CancellationToken cancellationToken)
    {
        var organization = await _context.Organizations
            .Include(o => o.Members)
            .ThenInclude(m => m.UserProfile)
            .FirstOrDefaultAsync(o => o.Id == request.OrganizationId, cancellationToken);
        
        var maxStorageForOrganization = 1000 * 1024 * 1024;

        if (organization == null)
            return new UnauthorizedError("Organization not found");

        if (!organization.Members.Any(m => m.UserProfileId == request.UserId))
        {
            return new UnauthorizedError("You are not a member of this organization");
        }

        var assets = await _context.OrganizationAssets
            .Where(a => a.OrganizationId == request.OrganizationId)
            .Select(a => new OrganizationAssetDto
            {
                Id = a.Id,
                FileName = a.FileName,
                FileUrl = a.FileUrl,
                FileSize = a.FileSize,
                ContentType = a.ContentType,
                CreatedAt = a.CreatedAt,
            })
            .OrderByDescending(a => a.CreatedAt)
            .ToListAsync(cancellationToken);

        
        var response = new AssetsResponseDto
        {
            Assets = assets,
            AvailableStorage = maxStorageForOrganization,
            UsedStorage = assets.Select(a => a.FileSize).Sum()
        };
        
        return response;
    }
}