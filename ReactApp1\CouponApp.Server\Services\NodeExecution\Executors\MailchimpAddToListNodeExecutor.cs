﻿using CouponApp.Server.Data;
using CouponApp.Server.Features.Campaigns;
using CouponApp.Server.Features.Mailchimp;
using CouponApp.Server.Features.Organizations;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Services.Integrations;
using CouponApp.Server.Services.NodeExecution.Interfaces;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;
using Polly;
using Polly.Retry;

namespace CouponApp.Server.Services.Executors;

public class MailchimpAddToListNodeExecutor : INodeExecutor
{
    private readonly ILogger<MailchimpAddToListNodeExecutor> _logger;
    private readonly AsyncRetryPolicy _retryPolicy;
    private readonly IVariablesStringInterpolationService _variablesStringInterpolationService;
    private readonly IMediator _mediator;
    private readonly ApplicationDbContext _context;
    public MailchimpAddToListNodeExecutor(
        ILogger<MailchimpAddToListNodeExecutor> logger,
        IVariablesStringInterpolationService variablesStringInterpolationService,
        IMediator mediator, ApplicationDbContext context)
    {
        _logger = logger;
        _variablesStringInterpolationService = variablesStringInterpolationService;
        _mediator = mediator;
        _context = context;
        _retryPolicy = Policy
            .Handle<HttpRequestException>()
            .WaitAndRetryAsync(3, retryAttempt =>
                TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));
    }

    public async Task<OneOf<Success, Error<string>>> ExecuteAsync(Guid campaignId, CampaignFlowNode node,
        Dictionary<string, object> variables)
    {
        try
        {
            var campaign = await _context.Campaigns.FirstOrDefaultAsync(c => c.Id == campaignId);
            if (campaign == null)
            {
                return new Error<string>("Campaign not found");
            }

            var integrationResult = await _mediator.Send(new GetIntegrationByIdQuery(campaign.WorkspaceId, IntegrationType.Mailchimp));
            if (!integrationResult.TryPickT0(out var integration, out var _))
            {
                return new Error<string>("Mailchimp integration not found. Please create a Mailchimp integration first.");
            }

            node.Payload.TryGetValue("listId", out var rawListId);
            node.Payload.TryGetValue("email", out var rawEmail);

            var listId = _variablesStringInterpolationService.InterpolateVariables(rawListId?.ToString() ?? string.Empty, variables);
            var email = _variablesStringInterpolationService.InterpolateVariables(rawEmail?.ToString() ?? string.Empty, variables);

            var addToListResult = await _mediator.Send(new AddEmailToMailchimpListCommand(integration.ConnectionId, listId, email));
            if (!addToListResult.TryPickT0(out var _, out var error))
            {
                return new Error<string>($"Failed to add email to Mailchimp list: {error.Value}");
            }

            return new Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing MailchimpAddToListNodeExecutor node {NodeId}", node.Id);
            return new Error<string>($"Error executing MailchimpAddToListNodeExecutor: {ex.Message}");
        }
    }
}