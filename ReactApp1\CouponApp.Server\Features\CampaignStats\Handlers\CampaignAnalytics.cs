﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CouponApp.Server.Models.Entities
{
    public class CampaignAnalytics : BaseEntity
    {
        public Campaign Campaign { get; set; }

        public int TotalClicks { get; set; }
        public int UniqueClicks { get; set; }

        public ICollection<CampaignUniqueEnter> UniqueEnters { get; set; } = new List<CampaignUniqueEnter>();
    }
}