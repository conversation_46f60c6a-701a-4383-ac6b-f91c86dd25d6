﻿using Microsoft.AspNetCore.Mvc;

namespace CouponApp.Server.Controllers;

[ApiController]
[Route("api/nango/webhook")]
public class NangoWebhooksController : ControllerBase
{
    
    public static string test = Guid.NewGuid().ToString();
    
    [HttpGet]
    public IActionResult Get()
    {
        return Ok("Healthy " + test);
    }    
    [HttpPost]
    public async Task<IActionResult> Post()
    {
        //TODO VERY IMPORTANT, HMAC VERIFICATION!
        
        using var reader = new StreamReader(Request.Body);
        var requestBody = await reader.ReadToEndAsync();
        
        return Ok("Healthy " + test);
    }
    
}