﻿namespace CouponApp.Server.Models.DTOs;

public class InvitationDto
{
    public Guid Id { get; set; }
    public string Email { get; set; }
    public DateTime ExpiresAt { get; set; }

    public string InvitedByUser { get; set; }
}

public class InviteUserDto
{
    public string Email { get; set; }
    public Guid OrganizationId { get; set; }
}

public class AcceptInvitationDto
{
    public string Token { get; set; }
}

public class InvitationDetailsDto
{
    public string OrganizationName { get; set; }
    public string OrganizationId { get; set; }
    public string OrganizationShortId { get; set; }
    public string InviterEmail { get; set; }
}