﻿using CouponApp.Server.Models.DTOs.Mailchimp;
using CouponApp.Server.Services;
using CouponApp.Server.Services.Integrations;
using Mediator;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Mailchimp.Handlers;

public class GetMailchimpListsHandler : IRequestHandler<GetMailchimpListsQuery, OneOf<MailchimpListsResponseDto, Error<string>>>
{
    private readonly INangoProxyClient _nangoProxyClient;

    public GetMailchimpListsHandler(INangoProxyServiceFactory nangoProxyServiceFactory)
    {
        _nangoProxyClient = nangoProxyServiceFactory.Create(IntegrationType.Mailchimp);
    }

    public async ValueTask<OneOf<MailchimpListsResponseDto, Error<string>>> Handle(GetMailchimpListsQuery request, CancellationToken cancellationToken)
    {
        return await _nangoProxyClient.GetAsync<MailchimpListsResponseDto>("lists", request.ConnectionId);
    }
}
