﻿using CouponApp.Server.Data;
using CouponApp.Server.Services.Integrations;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Organizations.Handlers;

public class DisconnectIntegrationHandler : IRequestHandler<DisconnectIntegrationCommand, OneOf<Success, Error<string>>>
{
    private readonly ApplicationDbContext _context;
    private readonly INangoClient _nangoClient;

    public DisconnectIntegrationHandler(ApplicationDbContext context, INangoClient nangoClient)
    {
        _context = context;
        _nangoClient = nangoClient;
    }

    public async ValueTask<OneOf<Success, Error<string>>> Handle(DisconnectIntegrationCommand request, CancellationToken cancellationToken)
    {
        var integration = await _context.OrganizationIntegrations
            .FirstOrDefaultAsync(oi => oi.OrganizationId == request.OrganizationId && oi.IntegrationType == request.IntegrationType, cancellationToken);

        if (integration == null)
        {
            return new Error<string>("Integration not found");
        }
        
        

        try
        {
            if (integration.IntegrationType.IsNangoIntegration())
            {
                var response = await _nangoClient.DeleteAsync<string>(
                    $"connection/{integration.ConnectionId}?provider_config_key={integration.IntegrationType.ToNangoProviderConfigKey()}");
                if (!response.TryPickT0(out var _, out var error))
                {
                    return new Error<string>("Could not disconnect: " + error.Value);
                }
            }

            _context.OrganizationIntegrations.Remove(integration);
            await _context.SaveChangesAsync(cancellationToken);
            return new Success();
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            return new Error<string>("Failed to disconnect integration");
        }
    }
}