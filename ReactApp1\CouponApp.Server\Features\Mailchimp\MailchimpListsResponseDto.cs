﻿namespace CouponApp.Server.Models.DTOs.Mailchimp;

using System.Text.Json.Serialization;

    public class MailchimpListsResponseDto
    {
        [JsonPropertyName("lists")]
        public List<MailchimpListMiniDto>? Lists { get; set; }

         [JsonPropertyName("total_items")]
         public int TotalItems { get; set; }

        // [JsonPropertyName("constraints")]
        // public MailchimpConstraintsDto? Constraints { get; set; }
        //
        // [JsonPropertyName("_links")]
        // public List<MailchimpLinkDto>? Links { get; set; }
    }

    public class MailchimpListMiniDto
    {
        [JsonPropertyName("id")]
        public string? Id { get; set; }
        
        [JsonPropertyName("name")]
        public string? Name { get; set; }
        
        [JsonPropertyName("double_optin")]
        public bool DoubleOptin { get; set; }
    }


    public class MailchimpListDto
    {
        [JsonPropertyName("id")]
        public string? Id { get; set; }

        [JsonPropertyName("web_id")]
        public int WebId { get; set; }

        [JsonPropertyName("name")]
        public string? Name { get; set; }

        [JsonPropertyName("contact")]
        public MailchimpContactDto? Contact { get; set; }

        [JsonPropertyName("permission_reminder")]
        public string? PermissionReminder { get; set; }

        [JsonPropertyName("use_archive_bar")]
        public bool UseArchiveBar { get; set; }

        [JsonPropertyName("campaign_defaults")]
        public MailchimpCampaignDefaultsDto? CampaignDefaults { get; set; }

        [JsonPropertyName("notify_on_subscribe")]
        public string? NotifyOnSubscribe { get; set; }

        [JsonPropertyName("notify_on_unsubscribe")]
        public string? NotifyOnUnsubscribe { get; set; }

        [JsonPropertyName("date_created")]
        public DateTime? DateCreated { get; set; }

        [JsonPropertyName("list_rating")]
        public int ListRating { get; set; }

        [JsonPropertyName("email_type_option")]
        public bool EmailTypeOption { get; set; }

        [JsonPropertyName("subscribe_url_short")]
        public string? SubscribeUrlShort { get; set; }

        [JsonPropertyName("subscribe_url_long")]
        public string? SubscribeUrlLong { get; set; }

        [JsonPropertyName("beamer_address")]
        public string? BeamerAddress { get; set; }

        [JsonPropertyName("visibility")]
        public string? Visibility { get; set; }

        [JsonPropertyName("double_optin")]
        public bool DoubleOptin { get; set; }

        [JsonPropertyName("has_welcome")]
        public bool HasWelcome { get; set; }

        [JsonPropertyName("marketing_permissions")]
        public bool MarketingPermissions { get; set; }

        [JsonPropertyName("modules")]
        public List<string>? Modules { get; set; }

        [JsonPropertyName("stats")]
        public MailchimpListStatsDto? Stats { get; set; }

        [JsonPropertyName("_links")]
        public List<MailchimpLinkDto>? Links { get; set; }
    }

    public class MailchimpContactDto
    {
        [JsonPropertyName("company")]
        public string? Company { get; set; }

        [JsonPropertyName("address1")]
        public string? Address1 { get; set; }

        [JsonPropertyName("address2")]
        public string? Address2 { get; set; }

        [JsonPropertyName("city")]
        public string? City { get; set; }

        [JsonPropertyName("state")]
        public string? State { get; set; }

        [JsonPropertyName("zip")]
        public string? Zip { get; set; }

        [JsonPropertyName("country")]
        public string? Country { get; set; }

        [JsonPropertyName("phone")]
        public string? Phone { get; set; }
    }

    public class MailchimpCampaignDefaultsDto
    {
        [JsonPropertyName("from_name")]
        public string? FromName { get; set; }

        [JsonPropertyName("from_email")]
        public string? FromEmail { get; set; }

        [JsonPropertyName("subject")]
        public string? Subject { get; set; }

        [JsonPropertyName("language")]
        public string? Language { get; set; }
    }

    public class MailchimpListStatsDto
    {
        [JsonPropertyName("member_count")]
        public int MemberCount { get; set; }

        [JsonPropertyName("unsubscribe_count")]
        public int UnsubscribeCount { get; set; }

        [JsonPropertyName("cleaned_count")]
        public int CleanedCount { get; set; }

        [JsonPropertyName("member_count_since_send")]
        public int MemberCountSinceSend { get; set; }

        [JsonPropertyName("unsubscribe_count_since_send")]
        public int UnsubscribeCountSinceSend { get; set; }

        [JsonPropertyName("cleaned_count_since_send")]
        public int CleanedCountSinceSend { get; set; }

        [JsonPropertyName("campaign_count")]
        public int CampaignCount { get; set; }

        [JsonPropertyName("campaign_last_sent")]
        public string? CampaignLastSent { get; set; }

        [JsonPropertyName("merge_field_count")]
        public int MergeFieldCount { get; set; }

        [JsonPropertyName("avg_sub_rate")]
        public double AvgSubRate { get; set; }

        [JsonPropertyName("avg_unsub_rate")]
        public double AvgUnsubRate { get; set; }

        [JsonPropertyName("target_sub_rate")]
        public double TargetSubRate { get; set; }

        [JsonPropertyName("open_rate")]
        public double OpenRate { get; set; }

        [JsonPropertyName("click_rate")]
        public double ClickRate { get; set; }

        [JsonPropertyName("last_sub_date")]
        public string? LastSubDate { get; set; }

        [JsonPropertyName("last_unsub_date")]
        public string? LastUnsubDate { get; set; }
    }

    public class MailchimpConstraintsDto
    {
        [JsonPropertyName("may_create")]
        public bool MayCreate { get; set; }

        [JsonPropertyName("max_instances")]
        public int MaxInstances { get; set; }

        [JsonPropertyName("current_total_instances")]
        public int CurrentTotalInstances { get; set; }
    }

    public class MailchimpLinkDto
    {
        [JsonPropertyName("rel")]
        public string? Rel { get; set; }

        [JsonPropertyName("href")]
        public string? Href { get; set; }

        [JsonPropertyName("method")]
        public string? Method { get; set; }

        [JsonPropertyName("targetSchema")]
        public string? TargetSchema { get; set; }

        [JsonPropertyName("schema")]
        public string? Schema { get; set; }
    }
