﻿using CouponApp.Server.Data;
using CouponApp.Server.Helpers;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Services.Integrations;
using Mediator;
using Microsoft.Extensions.Caching.Distributed;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Services;

public interface INangoConnectionIdService
{
    public  Task<OneOf<NangoConnectionCreateData, Error<string>>> GenerateConnectionCreationData(Guid organizationId, IntegrationType integrationType);
}

public class NangoConnectionIdService : INangoConnectionIdService
{
    
    private readonly ApplicationDbContext _context;
    private readonly IDistributedCache _cache;

    private readonly string _hmacSecretKey;

    public NangoConnectionIdService( ApplicationDbContext context, IDistributedCache cache, IConfiguration configuration)
    {
        _context = context;
        _cache = cache;
        
        _hmacSecretKey = configuration.GetValue<string>("Nango:HmacSecretKey") ?? throw new ArgumentNullException("Nango:HmacSecretKey must not be null!");
    }

    public async Task<OneOf<NangoConnectionCreateData, Error<string>>> GenerateConnectionCreationData(Guid organizationId, IntegrationType integrationType)
    {
        
        var organization = await _context.Organizations.FindAsync(organizationId);
        if (organization == null)
        {
            return new Error<string>("Organization not found");
        }

        var token = await GetTokenForConnectionAsync(organizationId);
        
        var connectionId = $"{organization.ShortId}-{token}";
        
        var message = $"{integrationType.ToNangoProviderConfigKey()}:{connectionId}";
        var digestMessage = HmacHelper.ComputeHmacSha256(_hmacSecretKey, message);

        return new NangoConnectionCreateData
        {
            Digest = digestMessage,
            ConnectionId = connectionId,
        };
    }

    public async Task<string> GetTokenForConnectionAsync(Guid organizationId)
    {
        var key = $"{organizationId}";
        var token = await _cache.GetStringAsync(key);

        if (token != null) return token;
        
        token = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString();
        
        await _cache.SetStringAsync(key, token, new DistributedCacheEntryOptions()
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30)
        });
        
        return token;

    }
    
}