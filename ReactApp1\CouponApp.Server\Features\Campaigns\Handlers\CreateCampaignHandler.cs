﻿using CouponApp.Server.Data;
using CouponApp.Server.Features.Campaigns;
using CouponApp.Server.Mappers;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Campaigns.Handlers;

public class CreateCampaignHandler : IRequestHandler<CreateCampaignCommand, OneOf<CampaignEditorDto, Error<string>>>
{
    private readonly ApplicationDbContext _context;

    public CreateCampaignHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<CampaignEditorDto, Error<string>>> Handle(CreateCampaignCommand request, CancellationToken cancellationToken)
    {
        var organizationIdGuid = request.CreateDto.OrganizationId;

        var campaign = new Campaign
        {
            Name = request.CreateDto.Name,
            CreatedByUserId = request.UserId,
            WorkspaceId = organizationIdGuid,
            CampaignAnalytics = new CampaignAnalytics(),
            Config = new CampaignConfigDto
            {
                // ActionNodes = request.CreateDto.Config.ActionNodes.Select(n => new CampaignFlowNode
                // {
                //     Id = n.Id,
                //     Type = n.Type,
                //     Position = n.Position,
                //     Connections = n.Connections,
                //     ElseConnection = n.ElseConnection,
                //     Payload = n.Payload
                // }).ToList(),
                Flows = request.CreateDto.Config.Flows,
                Scenes = request.CreateDto.Config.Scenes,
            } 
        };
        
        _context.Campaigns.Add(campaign);
        await _context.SaveChangesAsync(cancellationToken);
        return campaign.ToEditorDto();
    }
}