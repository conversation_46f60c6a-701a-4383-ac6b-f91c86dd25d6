﻿using CouponApp.Server.Models;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Errors;
using CouponApp.Server.Services;
using Mediator;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.CouponPacks;

public record UpdateCouponPackCommand(Guid Id, UpdateCouponPackDto UpdateDto) : IRequest<OneOf<CouponPackDto, NotFound>>;

public record CreateCouponPackCommand(CreateCouponPackDto CreateDto) : IRequest<CouponPackDto>;

public record AddCouponsToCouponPackCommand(Guid Id, List<string> CouponValues) : IRequest<OneOf<CouponPackDto, NotFound, CouponPackFullError>>;

public record DeleteCouponPackCommand(Guid Id) : IRequest<OneOf<Success, NotFound>>;

public record RedeemCouponCommand(Guid Id, SessionFingerprint Fingerprint) : IRequest<OneOf<CouponDto, NotFound, UserAlreadyParticipated>>;


