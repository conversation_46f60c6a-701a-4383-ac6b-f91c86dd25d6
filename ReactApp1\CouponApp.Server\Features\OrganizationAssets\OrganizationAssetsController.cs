﻿using CouponApp.Server.Attributes;
using CouponApp.Server.Extensions;
using CouponApp.Server.Features.OrganizationAssets;
using CouponApp.Server.Models.DTOs;
using Mediator;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CouponApp.Server.Controllers;

[ApiController]
[Route("api/organizations/{organizationId}/assets")]
[Authorize]
[RequirePermission(PermissionType.OrganizationRead, "organizationId")]
public class OrganizationAssetsController : ControllerBase
{
    private readonly IMediator _mediator;

    public OrganizationAssetsController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpPost]
    [Consumes("multipart/form-data")]
    public async Task<IActionResult> UploadAsset(Guid organizationId, [FromForm] IFormFile file)
    {
        var userId = User.GetId();

        var command = new UploadOrganizationAssetCommand(organizationId, userId, file);
        var result = await _mediator.Send(command);
        
        return result.Match<IActionResult>(
            asset => Ok(asset),
            error => BadRequest(error.Value),
            notFound => NotFound(),
            unauthorized => Unauthorized()
        );
    }

    [HttpGet]
    public async Task<IActionResult> GetAssets(Guid organizationId)
    {
        var userId = User.GetId();
        var query = new GetOrganizationAssetsQuery(organizationId, userId);
        var result = await _mediator.Send(query);
        return result.Match<IActionResult>(
            assets => Ok(assets),
            unauthorized => Unauthorized(unauthorized.Message)
        );
    }

    [HttpDelete("{assetId}")]
    public async Task<IActionResult> DeleteAsset(Guid organizationId, Guid assetId)
    {
        var userId = User.GetId();
        var command = new DeleteOrganizationAssetCommand(organizationId, userId, assetId);
        var result = await _mediator.Send(command);
        return result.Match<IActionResult>(
            success => NoContent(),
            notFound => NotFound(),
            unauthorized => Unauthorized(unauthorized.Message)
        );
    }
}