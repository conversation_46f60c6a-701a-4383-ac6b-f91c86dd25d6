﻿using CouponApp.Server.Data;
using CouponApp.Server.Features.Campaigns;
using CouponApp.Server.Features.Mailchimp;
using CouponApp.Server.Features.Organizations;
using CouponApp.Server.Services;
using CouponApp.Server.Services.Integrations;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Newsletter.Handlers;

public class AddUserToNewsletterHandler : IRequestHandler<AddUserToNewsletterCommand, OneOf<Success, NotFound, Error<string>>>
{

    private readonly IMediator _mediator;
    private readonly ApplicationDbContext _context;
    
    public AddUserToNewsletterHandler(IMediator mediator, ApplicationDbContext context)
    {
        _mediator = mediator;
        _context = context;
    }

    public async ValueTask<OneOf<Success, NotFound, Error<string>>> Handle(AddUserToNewsletterCommand request, CancellationToken cancellationToken)
    {
        var campaign = await _context.Campaigns.FirstOrDefaultAsync(c => c.Id == request.CampaignId, cancellationToken);
        if (campaign == null)
        {
            return new Error<string>("Campaign not found");
        }
  

        var integrationQuery = new GetIntegrationByIdQuery(campaign.WorkspaceId, IntegrationType.Mailchimp);
        var integrationResult = await _mediator.Send(integrationQuery);
        if (!integrationResult.TryPickT0(out var integration, out var _))
        {
            return new Error<string>("Mailchimp integration not found for this organization");
        }

        var addToListQuery = new AddEmailToMailchimpListCommand(integration.ConnectionId, request.ListId, request.Email);
        var addToListResult = await _mediator.Send(addToListQuery);
        if (!addToListResult.TryPickT0(out var _, out var error))
        {
            return error;
        }

        return new Success();
    }
}