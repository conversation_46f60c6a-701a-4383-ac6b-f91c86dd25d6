﻿using CouponApp.Server.Data;
using CouponApp.Server.Models.DTOs.Hubspot;
using CouponApp.Server.Services;
using CouponApp.Server.Services.Integrations;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Hubspot.Handlers;

public class GetHubspotPropertiesHandler : IRequestHandler<GetHubspotPropertiesQuery, OneOf<HubspotPropertiesResponseDto, Error<string>>>
{
    private readonly INangoProxyClient _nangoProxyClient;
    private readonly ApplicationDbContext _context;

    public GetHubspotPropertiesHandler(INangoProxyServiceFactory nangoProxyServiceFactory, ApplicationDbContext context)
    {
        _context = context;
        _nangoProxyClient = nangoProxyServiceFactory.Create(IntegrationType.Hubspot);
    }

    public async ValueTask<OneOf<HubspotPropertiesResponseDto, Error<string>>> Handle(GetHubspotPropertiesQuery request, CancellationToken cancellationToken)
    {
        
        var integration = await _context.OrganizationIntegrations
            .FirstOrDefaultAsync(oi => oi.OrganizationId == request.OrganizationId && oi.IntegrationType == IntegrationType.Hubspot, cancellationToken);
        
        if(integration?.ConnectionId == null)
        {
            return new Error<string>("No hubspot integration found");
        }
        
        var endpoint = "crm/v3/properties/contacts";
        var response = await _nangoProxyClient.GetAsync<HubspotPropertiesResponseDto>(endpoint, integration.ConnectionId);
        
        return response;
    }
}