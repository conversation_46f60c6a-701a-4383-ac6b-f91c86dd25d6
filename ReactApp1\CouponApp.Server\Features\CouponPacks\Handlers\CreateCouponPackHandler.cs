﻿using CouponApp.Server.Data;
using CouponApp.Server.Mappers;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;
using Mediator;

namespace CouponApp.Server.Features.CouponPacks.Handlers;

public class CreateCouponPackHandler : IRequestHandler<CreateCouponPackCommand, CouponPackDto>
{
    private readonly ApplicationDbContext _context;

    public CreateCouponPackHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<CouponPackDto> Handle(CreateCouponPackCommand request, CancellationToken cancellationToken)
    {
        var couponPack = new CouponPack
        {
            Name = request.CreateDto.Name,
            CampaignId = request.CreateDto.CampaignId,
            DeleteCouponOnRedeem = request.CreateDto.DeleteCouponOnRedeem,
            ShouldCheckingForUserParticipation = request.CreateDto.ShouldCheckingForUserParticipation,
            NotifyThreshold = request.CreateDto.NotifyThreshold,
            InterruptThreshold = request.CreateDto.InterruptThreshold
        };

        _context.CouponPacks.Add(couponPack);
        await _context.SaveChangesAsync(cancellationToken);

        return couponPack.ToDto();
    }
}