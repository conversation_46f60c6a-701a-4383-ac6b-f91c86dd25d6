﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CouponApp.Server.Models.Entities;

public class CouponPack : BaseEntity
{
    [Required]
    public string Name { get; set; }

    [Required]
    public Guid CampaignId { get; set; }

    [ForeignKey("CampaignId")]
    public Campaign Campaign { get; set; }

    public List<Coupon> Coupons { get; set; } 
    
    public bool DeleteCouponOnRedeem { get; set; }
    public bool ShouldCheckingForUserParticipation { get; set; }
    
    public int? NotifyThreshold { get; set; }
    public int? InterruptThreshold { get; set; }
    
}

public class Coupon
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid Id { get; set; }

    [Required]
    public string Value { get; set; }

    public Guid CouponPackId { get; set; }

    [ForeignKey("CouponPackId")]
    public CouponPack CouponPack { get; set; }
}