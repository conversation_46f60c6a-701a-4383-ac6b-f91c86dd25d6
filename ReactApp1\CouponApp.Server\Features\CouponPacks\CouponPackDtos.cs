﻿namespace CouponApp.Server.Models.DTOs;

public record CouponPackDto
{
    public Guid Id { get; init; }
    public string Name { get; init; }
    public Guid CampaignId { get; init; }
    public List<CouponDto> Coupons { get; init; }
    public DateTime CreatedAt { get; init; }
    public DateTime? UpdatedAt { get; init; }
    
    public bool DeleteCouponOnRedeem { get; init; }
    public bool ShouldCheckingForUserParticipation { get; init; }
    
    
    public int? NotifyThreshold { get; init; }
    public int? InterruptThreshold { get; init; }
    
}

public record CouponPackMiniDto
{
    public Guid Id { get; init; }
    public string Name { get; init; }
    public Guid CampaignId { get; init; }
    public DateTime CreatedAt { get; init; }
    public DateTime? UpdatedAt { get; init; }
    
    public int CouponsCount { get; init; }
    
    public bool DeleteCouponOnRedeem { get; init; }
    public bool ShouldCheckingForUserParticipation { get; init; }
    
    public int? NotifyThreshold { get; init; }
    public int? InterruptThreshold { get; init; }

}

public record CouponDto
{
    public Guid Id { get; init; }
    public string Value { get; init; }
}

public record CreateCouponPackDto
{
    public string Name { get; init; }
    public Guid CampaignId { get; init; }
    
    public bool DeleteCouponOnRedeem { get; init; }
    public bool ShouldCheckingForUserParticipation { get; init; }
    
    public int? NotifyThreshold { get; init; }
    public int? InterruptThreshold { get; init; }
}

public record AddCouponsDto
{
    public List<string> CouponValues { get; init; }
}

public record UpdateCouponPackDto
{
    public string Name { get; init; }
    public bool DeleteCouponOnRedeem { get; init; }
    
    public bool ShouldCheckingForUserParticipation { get; init; }
    
    public int? NotifyThreshold { get; init; }
    public int? InterruptThreshold { get; init; }
}