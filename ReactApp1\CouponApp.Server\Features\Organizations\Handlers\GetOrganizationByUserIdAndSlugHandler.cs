﻿// File: ~/CouponApp.Server/Features/Organizations/Handlers/GetOrganizationByUserIdAndSlugHandler.cs

using CouponApp.Server.Data;
using CouponApp.Server.Mappers;
using CouponApp.Server.Models.DTOs;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Organizations.Handlers;

public class GetOrganizationByUserIdAndSlugHandler : IRequestHandler<GetOrganizationForUserByShortIdQuery, OneOf<OrganizationDto, NotFound>>
{
    private readonly ApplicationDbContext _context;

    public GetOrganizationByUserIdAndSlugHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<OrganizationDto, NotFound>> Handle(GetOrganizationForUserByShortIdQuery request, CancellationToken cancellationToken)
    {
        var organization = await _context.Organizations
            .Include(o => o.Members)
            .Where(o =>  o.Members.Any(m => m.UserProfileId == request.UserId))
            .FirstOrDefaultAsync(o => o.ShortId == request.ShortId, cancellationToken);
        
        if (organization == null)
        {
            return new NotFound();
        }
        
        return organization.ToDto();
    }
}