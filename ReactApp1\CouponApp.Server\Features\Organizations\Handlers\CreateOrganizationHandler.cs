﻿// File: ~/CouponApp.Server/Features/Organizations/Handlers/CreateOrganizationHandler.cs

using CouponApp.Server.Data;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Models.Errors;
using CouponApp.Server.Services;
using Mediator;
using Microsoft.EntityFrameworkCore;
using NanoidDotNet;
using OneOf;

namespace CouponApp.Server.Features.Organizations.Handlers;

public class CreateOrganizationHandler : IRequestHandler<CreateOrganizationCommand, OneOf<OrganizationDto, OrganizationAlreadyExists, UserNotFound>>
{
    private readonly ApplicationDbContext _context;
    public CreateOrganizationHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<OrganizationDto, OrganizationAlreadyExists, UserNotFound>> Handle(CreateOrganizationCommand request, CancellationToken cancellationToken)
    {
        // var existingOrganization = await _context.Organizations.CountAsync(o => o.OwnerId == request.UserId, cancellationToken);
        var existingOrganization = await _context.UserOrganizations.CountAsync(o => o.UserProfileId == request.UserId && o.Role == UserOrganizationRole.Owner, cancellationToken);
        if (existingOrganization > 3)
        {
            return new OrganizationAlreadyExists("Maximum number of organizations reached for your plan.");
        }

        var user = await _context.UserProfiles.FirstOrDefaultAsync(u => u.Id == request.UserId, cancellationToken);

        if (user == null)
        {
            return new UserNotFound("User not found");
        }
        
        var organization = new Organization
        {
            ShortId = Nanoid.Generate(Nanoid.Alphabets.LettersAndDigits, 8),
            Name = request.Dto.Name
        };

        organization.Members.Add(new UserOrganization
        {
            UserProfileId = user.Id,
            Role = UserOrganizationRole.Owner
        });
        
        _context.Organizations.Add(organization);
        
        await _context.SaveChangesAsync(cancellationToken);

        return new OrganizationDto
        {
            Id = organization.Id,
            ShortId = organization.ShortId,
            Name = organization.Name,
            LogoUrl = organization.LogoUrl,
            MemberCount = 1 // Only the owner at this point
        };
    }
}