﻿using CouponApp.Server.Models;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Services.NodeExecution.Interfaces;
using OneOf.Types;

namespace CouponApp.Server.Services;
using OneOf;

public interface INodeExecutionService
{
    Task<OneOf<Success, Error<string>>> ExecuteNodesAsync(Guid campaignId, List<CampaignFlowNode> nodes, Dictionary<string, object> variables);
}

public class NodeExecutionService : INodeExecutionService
{
    private readonly INodeExecutorFactory _executorFactory;
    private readonly ILogger<NodeExecutionService> _logger;


    public NodeExecutionService(INodeExecutorFactory executorFactory, ILogger<NodeExecutionService> logger)
    {
        _executorFactory = executorFactory;
        _logger = logger;
    }

    public async Task<OneOf<Success, Error<string>>> ExecuteNodesAsync(Guid campaignId, List<CampaignFlowNode> nodes, Dictionary<string, object> variables)
    {
        foreach (var node in nodes)
        {
            var result = await ExecuteNodeAsync(campaignId, node, variables);
            if (result.TryPickT1(out var success, out var error))
            {
                return error;
            }
        }
        
        return new Success();
        
    }
    

    private async Task<OneOf<Success, Error<string>>> ExecuteNodeAsync(Guid campaignId, CampaignFlowNode node, Dictionary<string, object> variables)
    {

        try
        {
            var executor = _executorFactory.CreateExecutor(node.Type);
            return await executor.ExecuteAsync(campaignId,node, variables);
        }
        catch (ArgumentException ex)
        {
            _logger.LogError(ex, "Unsupported node type {NodeType}", node.Type);
            return new Error<string>($"Unsupported node type: {node.Type}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing node {NodeId}", node.Id);
            return new Error<string>($"Unexpected error: {ex.Message}");
        }
    }
    
}
