{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"ApplicationDbContextConnection": "Host=*************;Port=5432;Database=couponapp;Username=couponapp;Password=****************************************************************;Connection Lifetime=600"}, "Authentication": {"Google": {"ClientId": "1030742837396-unikcnfurebm7actiive13i40f11o4u0.apps.googleusercontent.com", "ClientSecret": "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"}}, "Minio": {"Endpoint": "dev-minio.beakbyte.com", "AccessKey": "Di7yvk4k0j2Kwg63ijqH", "SecretKey": "Nv0mLNAwMxwPTAgoNcix5od5fIpHcwrXAb2EdG3B", "BucketName": "couponapp", "PublicUrl": "https://dev-minio.beakbyte.com"}, "Plausible": {"ApiKey": "9jt34zulWLwpPKJqc0kUvpXH1oFC0789Sa8fcZqA-rkEbp8PzIUdU057tXuzXANC", "SiteId": "play.beakbyte.com"}, "Nango": {"HmacSecretKey": "ZlZ7Kb8I1AtKxkx6o16GRhFSN2kVtkXaQqFxjT5sErk7jV0WuHpcQZEoIAluGRQN"}, "Logto": {"BaseUrl": "https://aphogs.logto.app", "TokenEndpoint": "https://aphogs.logto.app/oidc/token", "ApiResource": "https://aphogs.logto.app/api", "ClientId": "skogcco8ody8ufhcqd3kk", "ClientSecret": "GNNytckStabo9QLfsycE6UrsStYKCh4f"}, "Cloudflare": {"ApiKey": "****************************************", "ZoneId": "0d30b74eb7842ff5826b855094b77bd1", "AccountId": "eab9c53d35f0fb8efc9a93f7ead2e358"}}