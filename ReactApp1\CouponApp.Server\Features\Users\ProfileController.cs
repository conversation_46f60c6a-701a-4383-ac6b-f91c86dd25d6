﻿using CouponApp.Server.Extensions;
using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Services;
using Mediator;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CouponApp.Server.Controllers
{
    [ApiController]
    [Route("api/user-profile")]
    [Authorize]
    public class UserProfileController : ControllerBase
    {
        private readonly IUserManagementService _userManagementService;
        private readonly IUserProfileService _userProfileService;
        
        public UserProfileController(IUserManagementService userManagementService, IUserProfileService userProfileService)
        {
            _userManagementService = userManagementService;
            _userProfileService = userProfileService;
        }
      
        
        [HttpGet]
        public async Task<IActionResult> GetUserProfile()
        {
            var userId = User.GetId();
            var result = await _userManagementService.GetUserProfileAsync(userId);

            return result.Match<IActionResult>(
                profile => Ok(profile),
                error => BadRequest(error.Value)
            );
        }

        [HttpPut]
        public async Task<IActionResult> UpdateUserProfile([FromBody] UpdateUserProfileDto dto)
        {
            var userId = User.GetId();
            var result = await _userManagementService.UpdateUserProfileAsync(userId, dto);

            return result.Match<IActionResult>(
                success => Ok(new { message = "Profile updated successfully" }),
                error => BadRequest(error.Value)
            );
        }
        
        [HttpPut("password")]
        public async Task<IActionResult> UpdateUserPassword([FromBody] UpdateUserPasswordDto dto)
        {
            var userId = User.GetId();
            var result = await _userManagementService.UpdateUserPasswordAsync(userId, dto);

            return result.Match<IActionResult>(
                success => Ok(new { message = "Profile updated successfully" }),
                error => BadRequest(error.Value)
            );
        }


    }

    public class UpdateProfileDto
    {
        public string Name { get; set; }
    }


    public class UpdatePasswordDto
    {
        public string NewPassword { get; set; }
    }

    public class VerifyPasswordDto
    {
        public string Password { get; set; }
    }
}
// EOF: '~/CouponApp.Server/Controllers/UserProfileController.cs'