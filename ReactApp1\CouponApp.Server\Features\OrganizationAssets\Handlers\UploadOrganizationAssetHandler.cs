﻿using CouponApp.Server.Data;
using CouponApp.Server.Models.DTOs.Organization;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Models.Errors;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using Minio;
using Minio.DataModel.Args;
using OneOf.Types;

namespace CouponApp.Server.Features.OrganizationAssets.Handlers;

public class UploadOrganizationAssetHandler 
    : IRequestHandler<UploadOrganizationAssetCommand, OneOf<OrganizationAssetDto, Error<string>, UnauthorizedError, NotFound>>
{
    private readonly ApplicationDbContext _context;
    private readonly IMinioClient _minioClient;
    private readonly IConfiguration _configuration;

    public UploadOrganizationAssetHandler(ApplicationDbContext context,  IConfiguration configuration, IMinioClient minioClient)
    {
        _context = context;

        _configuration = configuration;
        _minioClient = minioClient;
    }

    public async ValueTask<OneOf<OrganizationAssetDto, Error<string>, UnauthorizedError, NotFound>> Handle(
        UploadOrganizationAssetCommand request, CancellationToken cancellationToken)
    {
        var maxStorageForOrganization = 1000 * 1024 * 1024;
        
        var organization = await _context.Organizations
            .Include(o => o.Members)
            .ThenInclude(m => m.UserProfile)
            .FirstOrDefaultAsync(o => o.Id == request.OrganizationId, cancellationToken);

        if (organization == null)
            return new NotFound();

        var currentStorageUsed = await _context.OrganizationAssets
            .Where(o => o.OrganizationId == request.OrganizationId)
            .Select(o => o.FileSize)
            .SumAsync();
        

        if (!organization.Members.Any(m => m.UserProfileId == request.UserId))
        {
            return new UnauthorizedError("You are not a member of this organization");
        }

        if (request.File == null || request.File.Length == 0)
            return new Error<string>("File is empty");
        
        if (currentStorageUsed + request.File.Length > maxStorageForOrganization)
        {
            return new Error<string>("Uploading this file will result in exceeding your current plan storage limits.");
        }

        var extension = Path.GetExtension(request.File.FileName);
        var allowedExtensions = new[] { ".png", ".jpg", ".jpeg",".mp4", ".webp", ".avif", ".gif", ".svg", ".riv", ".mp3", ".wav", ".ogg"};
        if (!allowedExtensions.Contains(extension.ToLower()))
            return new Error<string>($"File extension {extension} is not allowed");

        var bucketName = _configuration["Minio:BucketName"]; // Use configured bucket name
        var minioPublicUrl = _configuration["Minio:PublicUrl"];
        var folderName = $"organization-{organization.Id}";

        // Ensure the bucket exists
        try
        {
            var bucketExistsArgs = new BucketExistsArgs().WithBucket(bucketName);
            var exists = await _minioClient.BucketExistsAsync(bucketExistsArgs, cancellationToken);
            if (!exists)
            {
                var makeBucketArgs = new MakeBucketArgs().WithBucket(bucketName);
                await _minioClient.MakeBucketAsync(makeBucketArgs, cancellationToken);
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
        }

        var fileName = $"{Guid.NewGuid()}{extension}";
        var objectName = $"{folderName}/{fileName}";

        using var stream = request.File.OpenReadStream();
        var response = await _minioClient.PutObjectAsync(new PutObjectArgs()
            .WithBucket(bucketName)
            .WithObject(objectName)
            .WithStreamData(stream)
            .WithObjectSize(stream.Length)
            .WithContentType(request.File.ContentType), cancellationToken);


        if (response.Etag == null)
        {
            return new Error<string>("Could not upload file.");
        }
        
        var asset = new OrganizationAsset
        {
            FileName = request.File.FileName,
            FileUrl = $"{minioPublicUrl}/{bucketName}/{objectName}",
            FileSize = request.File.Length,
            ContentType = request.File.ContentType,
            OrganizationId = organization.Id,
        };

        _context.OrganizationAssets.Add(asset);
        await _context.SaveChangesAsync(cancellationToken);

        var assetDto = new OrganizationAssetDto
        {
            Id = asset.Id,
            FileName = asset.FileName,
            FileUrl = asset.FileUrl,
            FileSize = asset.FileSize,
            ContentType = asset.ContentType,
            CreatedAt = asset.CreatedAt,
        };

        return assetDto;
    }
}