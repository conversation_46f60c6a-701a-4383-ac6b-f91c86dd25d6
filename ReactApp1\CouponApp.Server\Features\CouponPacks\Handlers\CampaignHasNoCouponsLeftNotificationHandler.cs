﻿using CouponApp.Server.Data;
using Mediator;
using Microsoft.EntityFrameworkCore;

namespace CouponApp.Server.Features.CouponPacks.Handlers;

public class CampaignHasNoCouponsLeftNotificationHandler :  INotificationHandler<CampaignHasNoCouponsLeftNotification>
{
    
    private readonly ApplicationDbContext _context;

    public CampaignHasNoCouponsLeftNotificationHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask Handle(CampaignHasNoCouponsLeftNotification notification, CancellationToken cancellationToken)
    {
        await _context.Campaigns
            .Where(c => c.Id == notification.CampaignId)
            .ExecuteUpdateAsync(setters => setters.SetProperty(campaign => campaign.HasNoCouponsLeft,  true), cancellationToken);
    }
}