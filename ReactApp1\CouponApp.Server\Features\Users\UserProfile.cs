﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CouponApp.Server.Models.Entities;

namespace CouponApp.Server.Data
{
    public class UserProfile 
    {
        [Key]
        public string Id { get; set; }

        public string? FullName { get; set; }
        
        public string? Email { get; set; }
        
        // public ICollection<WorkspaceMember> CampaignMembers { get; set; } = new List<WorkspaceMember>();
    }
}
