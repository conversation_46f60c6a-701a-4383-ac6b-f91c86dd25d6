﻿using CouponApp.Server.Models.DTOs;
using CouponApp.Server.Models.Enums;
using CouponApp.Server.Models.Errors;
using Mediator;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Campaigns;

public record CreateCampaignCommand(CampaignCreateDto CreateDto, string UserId) : IRequest<OneOf<CampaignEditorDto, Error<string>>>;

public record UpdateCampaignCommand(Guid Id, CampaignUpdateDto UpdateDto) : IRequest<OneOf<CampaignEditorDto, NotFound, Error<string>>>;

public record DeleteCampaignCommand(Guid Id) : IRequest<OneOf<Success, NotFound>>;

public record ExecuteNodeCommand(Guid CampaignId, string NodeId, Dictionary<string, object> Variables) : IRequest<OneOf<Success, NotFound, StringError>>;

