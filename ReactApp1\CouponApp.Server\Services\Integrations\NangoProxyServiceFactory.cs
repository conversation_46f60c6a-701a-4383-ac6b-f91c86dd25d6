﻿using CouponApp.Server.Models.Entities;
using CouponApp.Server.Services.Integrations;

namespace CouponApp.Server.Services;

public interface INangoProxyServiceFactory
{
    INangoProxyClient Create(IntegrationType integrationType);
}

public class NangoProxyServiceFactory : INangoProxyServiceFactory
{
    private readonly INangoClient _nangoClient;

    public NangoProxyServiceFactory(INangoClient nangoClient)
    {
        _nangoClient = nangoClient;
    }

    public INangoProxyClient Create(IntegrationType integrationType)
    {
        
        return integrationType switch
        {
            IntegrationType.Mailchimp => new NangoProxyClient(_nangoClient, integrationType.ToNangoProviderConfigKey(), "https://api.mailchimp.com/3.0/"),
            IntegrationType.Hubspot => new NangoProxyClient(_nangoClient, integrationType.ToNangoProviderConfigKey(), "https://api.hubapi.com/"),
            IntegrationType.Shopify => new NangoProxyClient(_nangoClient, integrationType.ToNangoProviderConfigKey()),
            _ => throw new ArgumentException($"Unsupported integration type: {integrationType}")
        };
    }




}