﻿using CouponApp.Server.Data;
using CouponApp.Server.Models;
using CouponApp.Server.Models.Entities;
using Microsoft.EntityFrameworkCore;
using OneOf;

namespace CouponApp.Server.Services;

public interface ICampaignSessionService
{
    Task<CampaignSession> GetOrCreateSessionAsync(Guid campaignId, SessionFingerprint fingerprint);
    Task<bool> HasParticipatedInCampaignAsync(Guid campaignId, SessionFingerprint fingerprint);
}

public class CampaignSessionService : ICampaignSessionService
{
    private readonly ApplicationDbContext _context;

    public CampaignSessionService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<CampaignSession> GetOrCreateSessionAsync(Guid campaignId, SessionFingerprint fingerprint)
    {

        var session = await _context.CampaignSessions
            .Where(cs => cs.CampaignId == campaignId)
            .Where(cs => cs.IpAddress == fingerprint.IpAddress)
            .Where(cs => cs.BrowserFingerprint == fingerprint.BrowserFingerprint)
            .Where(cs => cs.LocalStorageValue == fingerprint.LocalStorageValue)
            .AsSplitQuery()
            .FirstOrDefaultAsync();

        if (session != null) return session;
        
        session = new CampaignSession
        {
            CampaignId = campaignId,
            IpAddress = fingerprint.IpAddress,
            BrowserFingerprint = fingerprint.BrowserFingerprint,
            LocalStorageValue = fingerprint.LocalStorageValue,
        };
            
        _context.CampaignSessions.Add(session);
        await _context.SaveChangesAsync();



        return session;
    }
    
    

    public async Task<bool> HasParticipatedInCampaignAsync(Guid campaignId, SessionFingerprint? fingerprint)
    {
        var query = _context.CampaignSessions
            .Where(cs => cs.CampaignId == campaignId);

        if (!string.IsNullOrEmpty(fingerprint?.IpAddress))
        {
            query = query.Where(cs => cs.IpAddress == fingerprint.IpAddress);
        }

        if (!string.IsNullOrEmpty(fingerprint?.BrowserFingerprint))
        {
            query = query.Where(cs => cs.BrowserFingerprint == fingerprint.BrowserFingerprint);
        }

        if (!string.IsNullOrEmpty(fingerprint?.LocalStorageValue))
        {
            query = query.Where(cs => cs.LocalStorageValue == fingerprint.LocalStorageValue);
        }

        var participationCount = await query.CountAsync();

        // Adjust this threshold as needed
        const int participationThreshold = 1;

        return participationCount >= participationThreshold;
    }


}