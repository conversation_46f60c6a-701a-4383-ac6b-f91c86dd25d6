using CouponApp.Server.Data;
using CouponApp.Server.Features.CouponPacks;
using CouponApp.Server.Models.DTOs;
using Mediator;
using Microsoft.EntityFrameworkCore;

namespace CouponApp.Server.Features.CouponPacks.Handlers;

public class GetCouponPackCouponsLeftCountHandler : IRequestHandler<GetCouponPackCouponsLeftCountQuery, CouponPackCountDto>
{
    private readonly ApplicationDbContext _context;

    public GetCouponPackCouponsLeftCountHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<CouponPackCountDto> Handle(GetCouponPackCouponsLeftCountQuery query, CancellationToken cancellationToken)
    {
        var count = await _context.CouponPacks
            .Where(cp => cp.Id == query.CouponPackId)
            .Select(cp => new CouponPackCountDto
            {
                RemainingCoupons = cp.Coupons.Count()
            })
            .FirstOrDefaultAsync(cancellationToken);

        return count;
    }
}