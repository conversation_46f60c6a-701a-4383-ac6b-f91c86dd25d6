﻿using CouponApp.Server.Services.Integrations;

namespace CouponApp.Server.Models.DTOs;

public class OrganizationIntegrationDto
{
    public string Id { get; set; }
    public IntegrationType IntegrationType { get; set; }
    public string? ConnectionId { get; set; }
    public Dictionary<string, object>? AdditionalData { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}