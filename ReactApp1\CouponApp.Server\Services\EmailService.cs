﻿using OneOf.Types;
using OneOf;
using Resend;

namespace CouponApp.Server.Services;

public interface IEmailService
{
    Task<OneOf<Success, Error<string>>> SendInvitationEmailAsync(string email, string invitationLink);
    Task<OneOf<Success, Error<string>>> SendCouponsThresholdReachedEmailAsync(string emailAddress, string campaignName,  string couponPackName, string remainingCoupons);
}

public class EmailService : IEmailService
{
    private readonly ILogger<EmailService> _logger;
    private readonly IResend _resend;

    public EmailService(ILogger<EmailService> logger, IResend resend)
    {
        _logger = logger;
        _resend = resend;
    }

    public async Task<OneOf<Success, Error<string>>> SendInvitationEmailAsync(string email, string invitationLink)
    {
        try
        {
            _logger.LogInformation($"Sending invitation email to {email} with link: {invitationLink}");

            var message = new EmailMessage();
            message.From = "<EMAIL>";
            message.To.Add(email);
            message.Subject = "You have been invited to join CouponApp";
            message.HtmlBody =
                $"""<p>Hi,</p><p>You have been invited to join CouponApp. Please click the link below to accept the invitation.</p><p><a href='{invitationLink}'>Accept Invitation</a></p>""";


            var resendResponse = await _resend.EmailSendAsync(message);
            if (resendResponse.Success)
            {
                return new Success();
            }
        } 
        catch (Exception e)
        {
            _logger.LogError(e, "Error sending invitation email");
            return new Error<string>($"Error sending invitation email: {e.Message}");
        }
        return new Error<string>($"Unknown error happened while sending email.");
    }    
    
    
    public async Task<OneOf<Success, Error<string>>> SendCouponsThresholdReachedEmailAsync(string emailAddress, string campaignName,  string couponPackName, string remainingCoupons)
    {
        try
        {
            var emailSubject = $"Coupon Pack Threshold Reached: {couponPackName}";
            var emailBody = $"The coupon pack '{couponPackName}' in campaign '{campaignName}' has reached the notification threshold. Remaining coupons: {remainingCoupons}";

            var message = new EmailMessage();
            message.From = "<EMAIL>";
            message.To.Add(emailAddress);
            message.Subject = emailSubject;
            message.HtmlBody = emailBody;

            var resendResponse = await _resend.EmailSendAsync(message);
            if (resendResponse.Success)
            {
                return new Success();
            }
        } 
        catch (Exception e)
        {
            _logger.LogError(e, "Error sending Notification email");
            return new Error<string>($"Error sending Notification email: {e.Message}");
        }
        return new Error<string>($"Unknown error happened while Notification email.");
    }
}