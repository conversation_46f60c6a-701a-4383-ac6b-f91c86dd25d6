﻿using CouponApp.Server.Data;
using Mediator;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.Organizations.Handlers;

public class DeleteOrganizationHandler : IRequestHandler<DeleteOrganizationCommand, OneOf<Success, NotFound>>
{
    private readonly ApplicationDbContext _context;

    public DeleteOrganizationHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<Success, NotFound>> Handle(DeleteOrganizationCommand request, CancellationToken cancellationToken)
    {
        var organization = await _context.Organizations.FindAsync(new object[] { request.Id }, cancellationToken);
        if (organization == null)
        {
            return new NotFound();
        }

        _context.Organizations.Remove(organization);
        await _context.SaveChangesAsync(cancellationToken);

        return new Success();
    }
}