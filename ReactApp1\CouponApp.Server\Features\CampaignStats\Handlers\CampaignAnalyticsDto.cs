﻿using CouponApp.Server.Models.DTOs.Plausible;

namespace CouponApp.Server.Models.DTOs
{
    public record CampaignAnalyticsDto
    {
        public Guid CampaignId { get; init; }
        public string CampaignName { get; init; }
        public int TotalClicks { get; init; }
        public int UniqueClicks { get; init; }
        
        public List<PlausibleResultItemDto> EventAnalytics { get; init; }
        public List<PlausibleResultItemDto> VisitorAnalytics { get; init; }
    }


 
}