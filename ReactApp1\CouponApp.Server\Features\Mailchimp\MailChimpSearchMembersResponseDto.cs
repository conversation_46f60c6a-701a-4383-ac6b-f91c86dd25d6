﻿using System.Text.Json.Serialization;

namespace CouponApp.Server.Models.DTOs.Mailchimp;

public class MailchimpSearchMemberResponseDto
{
    [JsonPropertyName("exact_matches")]
    public MailchimpMemberMatchesDto ExactMatches { get; set; }

    [JsonPropertyName("full_search")]
    public MailchimpMemberMatchesDto FullSearch { get; set; }

    [JsonPropertyName("_links")]
    public List<MailchimpLinkDto> Links { get; set; }
}

public class MailchimpMemberMatchesDto
{
    [JsonPropertyName("members")]
    public List<MailchimpMemberDto> Members { get; set; }

    [JsonPropertyName("total_items")]
    public int TotalItems { get; set; }
}

public class MailchimpMemberDto
{
    [JsonPropertyName("id")]
    public string Id { get; set; }

    [JsonPropertyName("email_address")]
    public string EmailAddress { get; set; }

    [JsonPropertyName("unique_email_id")]
    public string UniqueEmailId { get; set; }

    [JsonPropertyName("contact_id")]
    public string ContactId { get; set; }

    [JsonPropertyName("full_name")]
    public string FullName { get; set; }

    [JsonPropertyName("web_id")]
    public int WebId { get; set; }

    [JsonPropertyName("email_type")]
    public string EmailType { get; set; }

    [JsonPropertyName("status")]
    public string Status { get; set; }

    [JsonPropertyName("consents_to_one_to_one_messaging")]
    public bool ConsentsToOneToOneMessaging { get; set; }

    [JsonPropertyName("sms_phone_number")]
    public string SmsPhoneNumber { get; set; }

    [JsonPropertyName("sms_subscription_status")]
    public string SmsSubscriptionStatus { get; set; }

    [JsonPropertyName("sms_subscription_last_updated")]
    public string SmsSubscriptionLastUpdated { get; set; }

    [JsonPropertyName("merge_fields")]
    public Dictionary<string, string> MergeFields { get; set; }

    [JsonPropertyName("stats")]
    public MailchimpMemberStatsDto Stats { get; set; }

    [JsonPropertyName("ip_signup")]
    public string IpSignup { get; set; }

    [JsonPropertyName("timestamp_signup")]
    public string TimestampSignup { get; set; }

    [JsonPropertyName("ip_opt")]
    public string IpOpt { get; set; }

    [JsonPropertyName("timestamp_opt")]
    public string TimestampOpt { get; set; }

    [JsonPropertyName("member_rating")]
    public int MemberRating { get; set; }

    [JsonPropertyName("last_changed")]
    public string LastChanged { get; set; }

    [JsonPropertyName("language")]
    public string Language { get; set; }

    [JsonPropertyName("vip")]
    public bool Vip { get; set; }

    [JsonPropertyName("email_client")]
    public string EmailClient { get; set; }

    [JsonPropertyName("location")]
    public MailchimpMemberLocationDto Location { get; set; }

    [JsonPropertyName("source")]
    public string Source { get; set; }

    [JsonPropertyName("tags_count")]
    public int TagsCount { get; set; }

    [JsonPropertyName("tags")]
    public List<MailchimpTagDto> Tags { get; set; }

    [JsonPropertyName("list_id")]
    public string ListId { get; set; }

    [JsonPropertyName("_links")]
    public List<MailchimpLinkDto> Links { get; set; }
}

public class MailchimpMemberStatsDto
{
    [JsonPropertyName("avg_open_rate")]
    public decimal AvgOpenRate { get; set; }

    [JsonPropertyName("avg_click_rate")]
    public decimal AvgClickRate { get; set; }
}

public class MailchimpMemberLocationDto
{
    [JsonPropertyName("latitude")]
    public decimal Latitude { get; set; }

    [JsonPropertyName("longitude")]
    public decimal Longitude { get; set; }

    [JsonPropertyName("gmtoff")]
    public int GmtOff { get; set; }

    [JsonPropertyName("dstoff")]
    public int DstOff { get; set; }

    [JsonPropertyName("country_code")]
    public string CountryCode { get; set; }

    [JsonPropertyName("timezone")]
    public string Timezone { get; set; }

    [JsonPropertyName("region")]
    public string Region { get; set; }
}

public class MailchimpTagDto
{
    [JsonPropertyName("id")]
    public int Id { get; set; }

    [JsonPropertyName("name")]
    public string Name { get; set; }
}

